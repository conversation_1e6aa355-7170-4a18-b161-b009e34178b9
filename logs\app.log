2025-07-08 12:02:26,583 - INFO - app - app.py:78 - Starting application initialization
2025-07-08 12:02:26,665 - INFO - app - app.py:80 - Page configuration completed
2025-07-08 12:02:26,665 - INFO - app - app.py:84 - Session state initialized
2025-07-08 12:02:26,673 - INFO - app - app.py:88 - User not authenticated, showing login form
2025-07-08 12:02:31,942 - INFO - app - app.py:78 - Starting application initialization
2025-07-08 12:02:31,942 - INFO - app - app.py:80 - Page configuration completed
2025-07-08 12:02:31,942 - INFO - app - app.py:84 - Session state initialized
2025-07-08 12:02:31,946 - INFO - app - app.py:88 - User not authenticated, showing login form
2025-07-08 12:02:33,512 - INFO - app - app.py:78 - Starting application initialization
2025-07-08 12:02:33,580 - INFO - app - app.py:80 - Page configuration completed
2025-07-08 12:02:33,580 - INFO - app - app.py:84 - Session state initialized
2025-07-08 12:02:33,580 - INFO - app - app.py:92 - User authenticated successfully
2025-07-08 12:02:33,584 - INFO - app - app.py:104 - Loading client data from database
2025-07-08 12:02:34,080 - INFO - app - app.py:113 - Successfully loaded data for 1 clients
2025-07-08 12:02:34,080 - INFO - app - app.py:61 - Successfully loaded image: logo/logo_integrum.jpg
2025-07-08 12:02:34,084 - INFO - app - app.py:144 - Client selection: None, Plant: None, Type: None
2025-07-08 12:02:34,084 - INFO - app - app.py:155 - Date range selected: 2025-07-08 to 2025-07-08
2025-07-08 12:02:34,095 - INFO - app - app.py:345 - No client selected, showing welcome message
2025-07-08 12:02:36,532 - INFO - app - app.py:78 - Starting application initialization
2025-07-08 12:02:36,532 - INFO - app - app.py:80 - Page configuration completed
2025-07-08 12:02:36,532 - INFO - app - app.py:84 - Session state initialized
2025-07-08 12:02:36,532 - INFO - app - app.py:92 - User authenticated successfully
2025-07-08 12:02:36,532 - INFO - app - app.py:104 - Loading client data from database
2025-07-08 12:02:36,729 - INFO - app - app.py:113 - Successfully loaded data for 1 clients
2025-07-08 12:02:36,729 - INFO - app - app.py:61 - Successfully loaded image: logo/logo_integrum.jpg
2025-07-08 12:02:36,742 - INFO - app - app.py:144 - Client selection: Kids Clinic India Limited, Plant: None, Type: Combined
2025-07-08 12:02:36,744 - INFO - app - app.py:155 - Date range selected: 2025-07-08 to 2025-07-08
2025-07-08 12:02:36,748 - INFO - app - app.py:183 - Created main application tabs
2025-07-08 12:02:36,751 - INFO - app - app.py:193 - Summary tab: Displaying data for Kids Clinic India Limited
2025-07-08 12:02:36,950 - INFO - app - app.py:217 - Successfully displayed generation vs consumption chart for Kids Clinic India Limited
2025-07-08 12:02:37,237 - INFO - app - app.py:228 - Successfully displayed hourly generation vs consumption chart for Kids Clinic India Limited
2025-07-08 12:02:37,435 - INFO - app - app.py:239 - Successfully displayed pie chart for Kids Clinic India Limited
2025-07-08 12:02:37,435 - INFO - app - app.py:250 - ToD Analysis tab: Displaying data for Kids Clinic India Limited
2025-07-08 12:02:38,356 - INFO - app - app.py:263 - Successfully displayed monthly ToD before banking for Kids Clinic India Limited
2025-07-08 12:02:39,738 - INFO - app - app.py:274 - Successfully displayed monthly banking settlement for Kids Clinic India Limited
2025-07-08 12:02:39,897 - INFO - app - app.py:285 - Successfully displayed ToD generation vs consumption for Kids Clinic India Limited
2025-07-08 12:02:40,064 - INFO - app - app.py:296 - Successfully displayed ToD generation analysis for Kids Clinic India Limited
2025-07-08 12:02:40,231 - INFO - app - app.py:307 - Successfully displayed ToD consumption analysis for Kids Clinic India Limited
2025-07-08 12:02:40,382 - INFO - app - app.py:318 - Successfully displayed mean trend vs irregularities analysis for Kids Clinic India Limited
2025-07-08 12:02:40,382 - INFO - app - app.py:329 - Power Cost Analysis tab: Displaying data for Kids Clinic India Limited
2025-07-08 12:02:41,682 - INFO - app - app.py:334 - Successfully displayed power cost analysis for Kids Clinic India Limited
2025-07-08 12:02:44,638 - INFO - app - app.py:78 - Starting application initialization
2025-07-08 12:02:44,638 - INFO - app - app.py:80 - Page configuration completed
2025-07-08 12:02:44,638 - INFO - app - app.py:84 - Session state initialized
2025-07-08 12:02:44,638 - INFO - app - app.py:92 - User authenticated successfully
2025-07-08 12:02:44,638 - INFO - app - app.py:104 - Loading client data from database
2025-07-08 12:02:44,900 - INFO - app - app.py:113 - Successfully loaded data for 1 clients
2025-07-08 12:02:44,922 - INFO - app - app.py:61 - Successfully loaded image: logo/logo_integrum.jpg
2025-07-08 12:02:44,922 - INFO - app - app.py:144 - Client selection: Kids Clinic India Limited, Plant: None, Type: Combined
2025-07-08 12:02:44,926 - INFO - app - app.py:155 - Date range selected: 2025-04-01 to 2025-04-01
2025-07-08 12:02:44,926 - INFO - app - app.py:183 - Created main application tabs
2025-07-08 12:02:44,926 - INFO - app - app.py:193 - Summary tab: Displaying data for Kids Clinic India Limited
2025-07-08 12:02:45,476 - INFO - app - app.py:217 - Successfully displayed generation vs consumption chart for Kids Clinic India Limited
2025-07-08 12:02:45,955 - INFO - app - app.py:78 - Starting application initialization
2025-07-08 12:02:45,955 - INFO - app - app.py:80 - Page configuration completed
2025-07-08 12:02:45,958 - INFO - app - app.py:84 - Session state initialized
2025-07-08 12:02:45,958 - INFO - app - app.py:92 - User authenticated successfully
2025-07-08 12:02:45,958 - INFO - app - app.py:104 - Loading client data from database
2025-07-08 12:02:46,165 - INFO - app - app.py:113 - Successfully loaded data for 1 clients
2025-07-08 12:02:46,165 - INFO - app - app.py:61 - Successfully loaded image: logo/logo_integrum.jpg
2025-07-08 12:02:46,177 - INFO - app - app.py:144 - Client selection: Kids Clinic India Limited, Plant: None, Type: Combined
2025-07-08 12:02:46,179 - INFO - app - app.py:155 - Date range selected: 2025-04-01 to 2025-04-30
2025-07-08 12:02:46,182 - INFO - app - app.py:183 - Created main application tabs
2025-07-08 12:02:46,182 - INFO - app - app.py:193 - Summary tab: Displaying data for Kids Clinic India Limited
2025-07-08 12:02:47,281 - INFO - app - app.py:217 - Successfully displayed generation vs consumption chart for Kids Clinic India Limited
2025-07-08 12:02:47,857 - INFO - app - app.py:228 - Successfully displayed hourly generation vs consumption chart for Kids Clinic India Limited
2025-07-08 12:02:49,056 - INFO - app - app.py:239 - Successfully displayed pie chart for Kids Clinic India Limited
2025-07-08 12:02:49,064 - INFO - app - app.py:250 - ToD Analysis tab: Displaying data for Kids Clinic India Limited
2025-07-08 12:02:49,938 - INFO - app - app.py:263 - Successfully displayed monthly ToD before banking for Kids Clinic India Limited
2025-07-08 12:02:51,156 - INFO - app - app.py:274 - Successfully displayed monthly banking settlement for Kids Clinic India Limited
2025-07-08 12:02:52,031 - INFO - app - app.py:285 - Successfully displayed ToD generation vs consumption for Kids Clinic India Limited
2025-07-08 12:02:52,586 - INFO - app - app.py:296 - Successfully displayed ToD generation analysis for Kids Clinic India Limited
2025-07-08 12:02:53,128 - INFO - app - app.py:307 - Successfully displayed ToD consumption analysis for Kids Clinic India Limited
2025-07-08 12:02:53,762 - INFO - app - app.py:318 - Successfully displayed mean trend vs irregularities analysis for Kids Clinic India Limited
2025-07-08 12:02:53,762 - INFO - app - app.py:329 - Power Cost Analysis tab: Displaying data for Kids Clinic India Limited
2025-07-08 12:02:55,042 - INFO - app - app.py:334 - Successfully displayed power cost analysis for Kids Clinic India Limited
2025-07-08 12:28:13,291 - INFO - app - app.py:78 - Starting application initialization
2025-07-08 12:28:13,293 - INFO - app - app.py:80 - Page configuration completed
2025-07-08 12:28:13,293 - INFO - app - app.py:84 - Session state initialized
2025-07-08 12:28:13,294 - INFO - app - app.py:92 - User authenticated successfully
2025-07-08 12:28:13,295 - INFO - app - app.py:104 - Loading client data from database
2025-07-08 12:28:13,404 - INFO - app - app.py:113 - Successfully loaded data for 1 clients
2025-07-08 12:28:13,405 - INFO - app - app.py:61 - Successfully loaded image: logo/logo_integrum.jpg
2025-07-08 12:28:13,406 - INFO - app - app.py:144 - Client selection: Kids Clinic India Limited, Plant: None, Type: Combined
2025-07-08 12:28:13,408 - INFO - app - app.py:155 - Date range selected: 2025-04-01 to 2025-04-30
2025-07-08 12:28:13,412 - INFO - app - app.py:183 - Created main application tabs
2025-07-08 12:28:13,414 - INFO - app - app.py:193 - Summary tab: Displaying data for Kids Clinic India Limited
2025-07-08 12:28:13,898 - INFO - app - app.py:217 - Successfully displayed generation vs consumption chart for Kids Clinic India Limited
2025-07-08 12:28:14,150 - INFO - app - app.py:228 - Successfully displayed hourly generation vs consumption chart for Kids Clinic India Limited
2025-07-08 12:28:14,655 - INFO - app - app.py:239 - Successfully displayed pie chart for Kids Clinic India Limited
2025-07-08 12:28:14,655 - INFO - app - app.py:250 - ToD Analysis tab: Displaying data for Kids Clinic India Limited
2025-07-08 12:28:15,024 - INFO - app - app.py:263 - Successfully displayed monthly ToD before banking for Kids Clinic India Limited
2025-07-08 12:28:16,328 - INFO - app - app.py:274 - Successfully displayed monthly banking settlement for Kids Clinic India Limited
2025-07-08 12:28:17,090 - INFO - app - app.py:285 - Successfully displayed ToD generation vs consumption for Kids Clinic India Limited
2025-07-08 12:28:17,328 - INFO - app - app.py:296 - Successfully displayed ToD generation analysis for Kids Clinic India Limited
2025-07-08 12:28:17,597 - INFO - app - app.py:307 - Successfully displayed ToD consumption analysis for Kids Clinic India Limited
2025-07-08 12:28:18,134 - INFO - app - app.py:318 - Successfully displayed mean trend vs irregularities analysis for Kids Clinic India Limited
2025-07-08 12:28:18,135 - INFO - app - app.py:329 - Power Cost Analysis tab: Displaying data for Kids Clinic India Limited
2025-07-08 12:28:19,863 - INFO - app - app.py:334 - Successfully displayed power cost analysis for Kids Clinic India Limited
2025-07-08 12:28:25,132 - INFO - app - app.py:78 - Starting application initialization
2025-07-08 12:28:25,132 - INFO - app - app.py:78 - Starting application initialization
2025-07-08 12:28:25,134 - INFO - app - app.py:80 - Page configuration completed
2025-07-08 12:28:25,285 - INFO - app - app.py:84 - Session state initialized
2025-07-08 12:28:25,293 - INFO - app - app.py:92 - User authenticated successfully
2025-07-08 12:28:25,313 - INFO - app - app.py:104 - Loading client data from database
2025-07-08 12:28:25,567 - INFO - app - app.py:113 - Successfully loaded data for 1 clients
2025-07-08 12:28:25,567 - INFO - app - app.py:61 - Successfully loaded image: logo/logo_integrum.jpg
2025-07-08 12:28:25,567 - INFO - app - app.py:144 - Client selection: Kids Clinic India Limited, Plant: None, Type: Combined
2025-07-08 12:28:25,567 - INFO - app - app.py:155 - Date range selected: 2025-04-01 to 2025-04-30
2025-07-08 12:28:25,581 - INFO - app - app.py:183 - Created main application tabs
2025-07-08 12:28:25,581 - INFO - app - app.py:193 - Summary tab: Displaying data for Kids Clinic India Limited
2025-07-08 12:28:26,389 - INFO - app - app.py:217 - Successfully displayed generation vs consumption chart for Kids Clinic India Limited
2025-07-08 12:28:26,813 - INFO - app - app.py:228 - Successfully displayed hourly generation vs consumption chart for Kids Clinic India Limited
2025-07-08 12:28:27,391 - INFO - app - app.py:239 - Successfully displayed pie chart for Kids Clinic India Limited
2025-07-08 12:28:27,391 - INFO - app - app.py:250 - ToD Analysis tab: Displaying data for Kids Clinic India Limited
2025-07-08 12:28:27,758 - INFO - app - app.py:263 - Successfully displayed monthly ToD before banking for Kids Clinic India Limited
2025-07-08 12:28:28,807 - INFO - app - app.py:274 - Successfully displayed monthly banking settlement for Kids Clinic India Limited
2025-07-08 12:28:29,302 - INFO - app - app.py:285 - Successfully displayed ToD generation vs consumption for Kids Clinic India Limited
2025-07-08 12:28:29,518 - INFO - app - app.py:296 - Successfully displayed ToD generation analysis for Kids Clinic India Limited
2025-07-08 12:28:29,716 - INFO - app - app.py:307 - Successfully displayed ToD consumption analysis for Kids Clinic India Limited
2025-07-08 12:28:30,115 - INFO - app - app.py:318 - Successfully displayed mean trend vs irregularities analysis for Kids Clinic India Limited
2025-07-08 12:28:30,115 - INFO - app - app.py:329 - Power Cost Analysis tab: Displaying data for Kids Clinic India Limited
2025-07-08 12:28:31,329 - INFO - app - app.py:334 - Successfully displayed power cost analysis for Kids Clinic India Limited
2025-07-08 14:53:58,618 - INFO - app - app.py:78 - Starting application initialization
2025-07-08 14:53:58,640 - INFO - app - app.py:80 - Page configuration completed
2025-07-08 14:53:58,640 - INFO - app - app.py:84 - Session state initialized
2025-07-08 14:53:58,643 - INFO - app - app.py:88 - User not authenticated, showing login form
2025-07-08 14:54:02,346 - INFO - app - app.py:78 - Starting application initialization
2025-07-08 14:54:02,346 - INFO - app - app.py:80 - Page configuration completed
2025-07-08 14:54:02,346 - INFO - app - app.py:84 - Session state initialized
2025-07-08 14:54:02,346 - INFO - app - app.py:88 - User not authenticated, showing login form
2025-07-08 14:54:03,995 - INFO - app - app.py:78 - Starting application initialization
2025-07-08 14:54:03,995 - INFO - app - app.py:80 - Page configuration completed
2025-07-08 14:54:03,995 - INFO - app - app.py:84 - Session state initialized
2025-07-08 14:54:03,995 - INFO - app - app.py:92 - User authenticated successfully
2025-07-08 14:54:03,995 - INFO - app - app.py:104 - Loading client data from database
2025-07-08 14:54:04,080 - INFO - app - app.py:113 - Successfully loaded data for 1 clients
2025-07-08 14:54:04,080 - INFO - app - app.py:61 - Successfully loaded image: logo/logo_integrum.jpg
2025-07-08 14:54:04,080 - INFO - app - app.py:144 - Client selection: None, Plant: None, Type: None
2025-07-08 14:54:04,095 - INFO - app - app.py:155 - Date range selected: 2025-07-08 to 2025-07-08
2025-07-08 14:54:04,101 - INFO - app - app.py:345 - No client selected, showing welcome message
2025-07-08 14:54:05,935 - INFO - app - app.py:78 - Starting application initialization
2025-07-08 14:54:05,935 - INFO - app - app.py:80 - Page configuration completed
2025-07-08 14:54:05,935 - INFO - app - app.py:84 - Session state initialized
2025-07-08 14:54:05,935 - INFO - app - app.py:92 - User authenticated successfully
2025-07-08 14:54:05,939 - INFO - app - app.py:104 - Loading client data from database
2025-07-08 14:54:06,025 - INFO - app - app.py:113 - Successfully loaded data for 1 clients
2025-07-08 14:54:06,026 - INFO - app - app.py:61 - Successfully loaded image: logo/logo_integrum.jpg
2025-07-08 14:54:06,029 - INFO - app - app.py:144 - Client selection: Kids Clinic India Limited, Plant: None, Type: Combined
2025-07-08 14:54:06,032 - INFO - app - app.py:155 - Date range selected: 2025-07-08 to 2025-07-08
2025-07-08 14:54:06,036 - INFO - app - app.py:183 - Created main application tabs
2025-07-08 14:54:06,037 - INFO - app - app.py:193 - Summary tab: Displaying data for Kids Clinic India Limited
2025-07-08 14:54:06,145 - INFO - app - app.py:217 - Successfully displayed generation vs consumption chart for Kids Clinic India Limited
2025-07-08 14:54:06,253 - INFO - app - app.py:228 - Successfully displayed hourly generation vs consumption chart for Kids Clinic India Limited
2025-07-08 14:54:06,383 - INFO - app - app.py:239 - Successfully displayed pie chart for Kids Clinic India Limited
2025-07-08 14:54:06,383 - INFO - app - app.py:250 - ToD Analysis tab: Displaying data for Kids Clinic India Limited
2025-07-08 14:54:06,791 - INFO - app - app.py:263 - Successfully displayed monthly ToD before banking for Kids Clinic India Limited
2025-07-08 14:54:08,509 - INFO - app - app.py:274 - Successfully displayed monthly banking settlement for Kids Clinic India Limited
2025-07-08 14:54:08,576 - INFO - app - app.py:285 - Successfully displayed ToD generation vs consumption for Kids Clinic India Limited
2025-07-08 14:54:08,649 - INFO - app - app.py:296 - Successfully displayed ToD generation analysis for Kids Clinic India Limited
2025-07-08 14:54:08,736 - INFO - app - app.py:307 - Successfully displayed ToD consumption analysis for Kids Clinic India Limited
2025-07-08 14:54:08,821 - INFO - app - app.py:318 - Successfully displayed mean trend vs irregularities analysis for Kids Clinic India Limited
2025-07-08 14:54:08,822 - INFO - app - app.py:329 - Power Cost Analysis tab: Displaying data for Kids Clinic India Limited
2025-07-08 14:54:10,367 - INFO - app - app.py:334 - Successfully displayed power cost analysis for Kids Clinic India Limited
2025-07-08 14:54:11,280 - INFO - app - app.py:78 - Starting application initialization
2025-07-08 14:54:11,280 - INFO - app - app.py:80 - Page configuration completed
2025-07-08 14:54:11,280 - INFO - app - app.py:84 - Session state initialized
2025-07-08 14:54:11,280 - INFO - app - app.py:92 - User authenticated successfully
2025-07-08 14:54:11,280 - INFO - app - app.py:104 - Loading client data from database
2025-07-08 14:54:11,370 - INFO - app - app.py:113 - Successfully loaded data for 1 clients
2025-07-08 14:54:11,370 - INFO - app - app.py:61 - Successfully loaded image: logo/logo_integrum.jpg
2025-07-08 14:54:11,370 - INFO - app - app.py:144 - Client selection: Kids Clinic India Limited, Plant: None, Type: Combined
2025-07-08 14:54:11,370 - INFO - app - app.py:155 - Date range selected: 2025-04-01 to 2025-04-01
2025-07-08 14:54:11,380 - INFO - app - app.py:183 - Created main application tabs
2025-07-08 14:54:11,380 - INFO - app - app.py:193 - Summary tab: Displaying data for Kids Clinic India Limited
2025-07-08 14:54:11,772 - INFO - app - app.py:217 - Successfully displayed generation vs consumption chart for Kids Clinic India Limited
2025-07-08 14:54:11,943 - INFO - app - app.py:228 - Successfully displayed hourly generation vs consumption chart for Kids Clinic India Limited
2025-07-08 14:54:12,179 - INFO - app - app.py:239 - Successfully displayed pie chart for Kids Clinic India Limited
2025-07-08 14:54:12,181 - INFO - app - app.py:250 - ToD Analysis tab: Displaying data for Kids Clinic India Limited
2025-07-08 14:54:12,232 - INFO - app - app.py:78 - Starting application initialization
2025-07-08 14:54:12,233 - INFO - app - app.py:80 - Page configuration completed
2025-07-08 14:54:12,233 - INFO - app - app.py:84 - Session state initialized
2025-07-08 14:54:12,235 - INFO - app - app.py:92 - User authenticated successfully
2025-07-08 14:54:12,235 - INFO - app - app.py:104 - Loading client data from database
2025-07-08 14:54:12,343 - INFO - app - app.py:113 - Successfully loaded data for 1 clients
2025-07-08 14:54:12,344 - INFO - app - app.py:61 - Successfully loaded image: logo/logo_integrum.jpg
2025-07-08 14:54:12,347 - INFO - app - app.py:144 - Client selection: Kids Clinic India Limited, Plant: None, Type: Combined
2025-07-08 14:54:12,348 - INFO - app - app.py:155 - Date range selected: 2025-04-01 to 2025-04-30
2025-07-08 14:54:12,352 - INFO - app - app.py:183 - Created main application tabs
2025-07-08 14:54:12,353 - INFO - app - app.py:193 - Summary tab: Displaying data for Kids Clinic India Limited
2025-07-08 14:54:13,034 - INFO - app - app.py:217 - Successfully displayed generation vs consumption chart for Kids Clinic India Limited
2025-07-08 14:54:13,330 - INFO - app - app.py:228 - Successfully displayed hourly generation vs consumption chart for Kids Clinic India Limited
2025-07-08 14:54:13,812 - INFO - app - app.py:239 - Successfully displayed pie chart for Kids Clinic India Limited
2025-07-08 14:54:13,812 - INFO - app - app.py:250 - ToD Analysis tab: Displaying data for Kids Clinic India Limited
2025-07-08 14:54:14,221 - INFO - app - app.py:263 - Successfully displayed monthly ToD before banking for Kids Clinic India Limited
2025-07-08 14:54:15,922 - INFO - app - app.py:274 - Successfully displayed monthly banking settlement for Kids Clinic India Limited
2025-07-08 14:54:16,647 - INFO - app - app.py:285 - Successfully displayed ToD generation vs consumption for Kids Clinic India Limited
2025-07-08 14:54:16,879 - INFO - app - app.py:296 - Successfully displayed ToD generation analysis for Kids Clinic India Limited
2025-07-08 14:54:17,104 - INFO - app - app.py:307 - Successfully displayed ToD consumption analysis for Kids Clinic India Limited
2025-07-08 14:54:17,665 - INFO - app - app.py:318 - Successfully displayed mean trend vs irregularities analysis for Kids Clinic India Limited
2025-07-08 14:54:17,665 - INFO - app - app.py:329 - Power Cost Analysis tab: Displaying data for Kids Clinic India Limited
2025-07-08 14:54:19,300 - INFO - app - app.py:334 - Successfully displayed power cost analysis for Kids Clinic India Limited
2025-07-08 15:48:01,682 - INFO - app - app.py:79 - Starting application initialization
2025-07-08 15:48:01,737 - INFO - app - app.py:81 - Page configuration completed
2025-07-08 15:48:01,738 - INFO - app - app.py:85 - Session state initialized
2025-07-08 15:48:01,739 - INFO - app - app.py:89 - User not authenticated, showing login form
2025-07-08 15:48:06,359 - INFO - app - app.py:79 - Starting application initialization
2025-07-08 15:48:06,362 - INFO - app - app.py:81 - Page configuration completed
2025-07-08 15:48:06,362 - INFO - app - app.py:85 - Session state initialized
2025-07-08 15:48:06,363 - INFO - app - app.py:89 - User not authenticated, showing login form
2025-07-08 15:48:07,937 - INFO - app - app.py:79 - Starting application initialization
2025-07-08 15:48:07,937 - INFO - app - app.py:81 - Page configuration completed
2025-07-08 15:48:07,948 - INFO - app - app.py:85 - Session state initialized
2025-07-08 15:48:07,948 - INFO - app - app.py:93 - User authenticated successfully
2025-07-08 15:48:07,948 - INFO - app - app.py:105 - Loading client data from database
2025-07-08 15:48:08,300 - INFO - app - app.py:114 - Successfully loaded data for 1 clients
2025-07-08 15:48:08,300 - INFO - app - app.py:62 - Successfully loaded image: logo/logo_integrum.jpg
2025-07-08 15:48:08,300 - INFO - app - app.py:145 - Client selection: None, Plant: None, Type: None
2025-07-08 15:48:08,300 - INFO - app - app.py:156 - Date range selected: 2025-07-08 to 2025-07-08
2025-07-08 15:48:08,300 - INFO - app - app.py:354 - No client selected, showing welcome message
2025-07-08 15:48:10,559 - INFO - app - app.py:79 - Starting application initialization
2025-07-08 15:48:10,559 - INFO - app - app.py:81 - Page configuration completed
2025-07-08 15:48:10,559 - INFO - app - app.py:85 - Session state initialized
2025-07-08 15:48:10,559 - INFO - app - app.py:93 - User authenticated successfully
2025-07-08 15:48:10,559 - INFO - app - app.py:105 - Loading client data from database
2025-07-08 15:48:10,644 - INFO - app - app.py:114 - Successfully loaded data for 1 clients
2025-07-08 15:48:10,645 - INFO - app - app.py:62 - Successfully loaded image: logo/logo_integrum.jpg
2025-07-08 15:48:10,648 - INFO - app - app.py:145 - Client selection: Kids Clinic India Limited, Plant: None, Type: Combined
2025-07-08 15:48:10,650 - INFO - app - app.py:156 - Date range selected: 2025-07-08 to 2025-07-08
2025-07-08 15:48:10,655 - INFO - app - app.py:184 - Created main application tabs
2025-07-08 15:48:10,655 - INFO - app - app.py:194 - Summary tab: Displaying data for Kids Clinic India Limited
2025-07-08 15:48:10,757 - INFO - app - app.py:218 - Successfully displayed generation vs consumption chart for Kids Clinic India Limited
2025-07-08 15:48:12,656 - INFO - app - app.py:230 - Successfully displayed monthly banking settlement for Kids Clinic India Limited
2025-07-08 15:48:12,728 - INFO - app - app.py:241 - Successfully displayed ToD generation analysis for Kids Clinic India Limited
2025-07-08 15:48:12,810 - INFO - app - app.py:252 - Successfully displayed ToD consumption analysis for Kids Clinic India Limited
2025-07-08 15:48:12,921 - INFO - app - app.py:263 - Successfully displayed hourly generation vs consumption chart for Kids Clinic India Limited
2025-07-08 15:48:12,922 - INFO - app - app.py:277 - ToD Analysis tab: Displaying data for Kids Clinic India Limited
2025-07-08 15:48:13,454 - INFO - app - app.py:290 - Successfully displayed monthly ToD before banking for Kids Clinic India Limited
2025-07-08 15:48:13,524 - INFO - app - app.py:303 - Successfully displayed ToD generation vs consumption for Kids Clinic India Limited
2025-07-08 15:48:13,585 - INFO - app - app.py:316 - Successfully displayed mean trend vs irregularities analysis for Kids Clinic India Limited
2025-07-08 15:48:13,756 - INFO - app - app.py:79 - Starting application initialization
2025-07-08 15:48:13,756 - INFO - app - app.py:81 - Page configuration completed
2025-07-08 15:48:13,756 - INFO - app - app.py:85 - Session state initialized
2025-07-08 15:48:13,756 - INFO - app - app.py:93 - User authenticated successfully
2025-07-08 15:48:13,756 - INFO - app - app.py:105 - Loading client data from database
2025-07-08 15:48:13,885 - INFO - app - app.py:114 - Successfully loaded data for 1 clients
2025-07-08 15:48:13,886 - INFO - app - app.py:62 - Successfully loaded image: logo/logo_integrum.jpg
2025-07-08 15:48:13,887 - INFO - app - app.py:145 - Client selection: Kids Clinic India Limited, Plant: None, Type: Combined
2025-07-08 15:48:13,887 - INFO - app - app.py:156 - Date range selected: 2025-05-01 to 2025-05-01
2025-07-08 15:48:13,887 - INFO - app - app.py:184 - Created main application tabs
2025-07-08 15:48:13,897 - INFO - app - app.py:194 - Summary tab: Displaying data for Kids Clinic India Limited
2025-07-08 15:48:14,383 - INFO - app - app.py:218 - Successfully displayed generation vs consumption chart for Kids Clinic India Limited
2025-07-08 15:48:14,636 - INFO - app - app.py:79 - Starting application initialization
2025-07-08 15:48:14,639 - INFO - app - app.py:81 - Page configuration completed
2025-07-08 15:48:14,639 - INFO - app - app.py:85 - Session state initialized
2025-07-08 15:48:14,641 - INFO - app - app.py:93 - User authenticated successfully
2025-07-08 15:48:14,642 - INFO - app - app.py:105 - Loading client data from database
2025-07-08 15:48:14,775 - INFO - app - app.py:114 - Successfully loaded data for 1 clients
2025-07-08 15:48:14,776 - INFO - app - app.py:62 - Successfully loaded image: logo/logo_integrum.jpg
2025-07-08 15:48:14,783 - INFO - app - app.py:145 - Client selection: Kids Clinic India Limited, Plant: None, Type: Combined
2025-07-08 15:48:14,785 - INFO - app - app.py:156 - Date range selected: 2025-05-01 to 2025-05-31
2025-07-08 15:48:14,786 - INFO - app - app.py:184 - Created main application tabs
2025-07-08 15:48:14,786 - INFO - app - app.py:194 - Summary tab: Displaying data for Kids Clinic India Limited
2025-07-08 15:48:15,553 - INFO - app - app.py:218 - Successfully displayed generation vs consumption chart for Kids Clinic India Limited
2025-07-08 15:48:17,827 - INFO - app - app.py:230 - Successfully displayed monthly banking settlement for Kids Clinic India Limited
2025-07-08 15:48:18,087 - INFO - app - app.py:241 - Successfully displayed ToD generation analysis for Kids Clinic India Limited
2025-07-08 15:48:18,304 - INFO - app - app.py:252 - Successfully displayed ToD consumption analysis for Kids Clinic India Limited
2025-07-08 15:48:19,123 - INFO - app - app.py:263 - Successfully displayed hourly generation vs consumption chart for Kids Clinic India Limited
2025-07-08 15:48:19,124 - INFO - app - app.py:277 - ToD Analysis tab: Displaying data for Kids Clinic India Limited
2025-07-08 15:48:19,537 - INFO - app - app.py:290 - Successfully displayed monthly ToD before banking for Kids Clinic India Limited
2025-07-08 15:48:20,209 - INFO - app - app.py:303 - Successfully displayed ToD generation vs consumption for Kids Clinic India Limited
2025-07-08 15:48:20,759 - INFO - app - app.py:316 - Successfully displayed mean trend vs irregularities analysis for Kids Clinic India Limited
2025-07-08 15:48:21,038 - INFO - app - app.py:327 - Successfully displayed monthly settled heatmap for Kids Clinic India Limited
2025-07-08 15:48:21,038 - INFO - app - app.py:338 - Power Cost Analysis tab: Displaying data for Kids Clinic India Limited
2025-07-08 15:48:22,687 - INFO - app - app.py:343 - Successfully displayed power cost analysis for Kids Clinic India Limited
2025-07-08 15:50:57,067 - INFO - app - app.py:79 - Starting application initialization
2025-07-08 15:50:57,069 - INFO - app - app.py:81 - Page configuration completed
2025-07-08 15:50:57,069 - INFO - app - app.py:85 - Session state initialized
2025-07-08 15:50:57,069 - INFO - app - app.py:93 - User authenticated successfully
2025-07-08 15:50:57,069 - INFO - app - app.py:105 - Loading client data from database
2025-07-08 15:50:57,168 - INFO - app - app.py:114 - Successfully loaded data for 1 clients
2025-07-08 15:50:57,170 - INFO - app - app.py:62 - Successfully loaded image: logo/logo_integrum.jpg
2025-07-08 15:50:57,173 - INFO - app - app.py:145 - Client selection: Kids Clinic India Limited, Plant: None, Type: Combined
2025-07-08 15:50:57,175 - INFO - app - app.py:156 - Date range selected: 2025-05-01 to 2025-05-31
2025-07-08 15:50:57,180 - INFO - app - app.py:184 - Created main application tabs
2025-07-08 15:50:57,182 - INFO - app - app.py:194 - Summary tab: Displaying data for Kids Clinic India Limited
2025-07-08 15:50:57,621 - INFO - app - app.py:218 - Successfully displayed generation vs consumption chart for Kids Clinic India Limited
2025-07-08 15:50:58,715 - INFO - app - app.py:230 - Successfully displayed monthly banking settlement for Kids Clinic India Limited
2025-07-08 15:50:58,918 - INFO - app - app.py:241 - Successfully displayed ToD generation analysis for Kids Clinic India Limited
2025-07-08 15:50:59,124 - INFO - app - app.py:252 - Successfully displayed ToD consumption analysis for Kids Clinic India Limited
2025-07-08 15:50:59,417 - INFO - app - app.py:263 - Successfully displayed hourly generation vs consumption chart for Kids Clinic India Limited
2025-07-08 15:50:59,418 - INFO - app - app.py:277 - ToD Analysis tab: Displaying data for Kids Clinic India Limited
2025-07-08 15:50:59,973 - INFO - app - app.py:294 - Successfully displayed ToD generation vs consumption for Kids Clinic India Limited
2025-07-08 15:51:00,424 - INFO - app - app.py:307 - Successfully displayed mean trend vs irregularities analysis for Kids Clinic India Limited
2025-07-08 15:51:00,804 - INFO - app - app.py:318 - Successfully displayed monthly settled heatmap for Kids Clinic India Limited
2025-07-08 15:51:00,805 - INFO - app - app.py:329 - Power Cost Analysis tab: Displaying data for Kids Clinic India Limited
2025-07-08 15:51:02,011 - INFO - app - app.py:334 - Successfully displayed power cost analysis for Kids Clinic India Limited
2025-07-08 15:51:15,403 - INFO - app - app.py:79 - Starting application initialization
2025-07-08 15:51:15,403 - INFO - app - app.py:81 - Page configuration completed
2025-07-08 15:51:15,404 - INFO - app - app.py:85 - Session state initialized
2025-07-08 15:51:15,404 - INFO - app - app.py:93 - User authenticated successfully
2025-07-08 15:51:15,406 - INFO - app - app.py:105 - Loading client data from database
2025-07-08 15:51:15,484 - INFO - app - app.py:114 - Successfully loaded data for 1 clients
2025-07-08 15:51:15,484 - INFO - app - app.py:62 - Successfully loaded image: logo/logo_integrum.jpg
2025-07-08 15:51:15,484 - INFO - app - app.py:145 - Client selection: Kids Clinic India Limited, Plant: None, Type: Combined
2025-07-08 15:51:15,501 - INFO - app - app.py:156 - Date range selected: 2025-05-01 to 2025-05-31
2025-07-08 15:51:15,504 - INFO - app - app.py:184 - Created main application tabs
2025-07-08 15:51:15,504 - INFO - app - app.py:194 - Summary tab: Displaying data for Kids Clinic India Limited
2025-07-08 15:51:15,999 - INFO - app - app.py:218 - Successfully displayed generation vs consumption chart for Kids Clinic India Limited
2025-07-08 15:51:17,105 - INFO - app - app.py:230 - Successfully displayed monthly banking settlement for Kids Clinic India Limited
2025-07-08 15:51:17,306 - INFO - app - app.py:241 - Successfully displayed ToD generation analysis for Kids Clinic India Limited
2025-07-08 15:51:17,506 - INFO - app - app.py:252 - Successfully displayed ToD consumption analysis for Kids Clinic India Limited
2025-07-08 15:51:17,785 - INFO - app - app.py:263 - Successfully displayed hourly generation vs consumption chart for Kids Clinic India Limited
2025-07-08 15:51:17,786 - INFO - app - app.py:277 - ToD Analysis tab: Displaying data for Kids Clinic India Limited
2025-07-08 15:51:18,223 - INFO - app - app.py:294 - Successfully displayed ToD generation vs consumption for Kids Clinic India Limited
2025-07-08 15:51:18,233 - INFO - app - app.py:311 - Power Cost Analysis tab: Displaying data for Kids Clinic India Limited
2025-07-08 15:51:19,177 - INFO - app - app.py:316 - Successfully displayed power cost analysis for Kids Clinic India Limited
2025-07-08 15:53:06,224 - INFO - app - app.py:79 - Starting application initialization
2025-07-08 15:53:06,224 - INFO - app - app.py:81 - Page configuration completed
2025-07-08 15:53:06,235 - INFO - app - app.py:85 - Session state initialized
2025-07-08 15:53:06,236 - INFO - app - app.py:93 - User authenticated successfully
2025-07-08 15:53:06,237 - INFO - app - app.py:105 - Loading client data from database
2025-07-08 15:53:06,326 - INFO - app - app.py:114 - Successfully loaded data for 1 clients
2025-07-08 15:53:06,326 - INFO - app - app.py:62 - Successfully loaded image: logo/logo_integrum.jpg
2025-07-08 15:53:06,326 - INFO - app - app.py:145 - Client selection: Kids Clinic India Limited, Plant: None, Type: Combined
2025-07-08 15:53:06,326 - INFO - app - app.py:156 - Date range selected: 2025-05-01 to 2025-05-31
2025-07-08 15:53:06,326 - INFO - app - app.py:184 - Created main application tabs
2025-07-08 15:53:06,326 - INFO - app - app.py:194 - Summary tab: Displaying data for Kids Clinic India Limited
2025-07-08 15:53:06,910 - INFO - app - app.py:218 - Successfully displayed generation vs consumption chart for Kids Clinic India Limited
2025-07-08 15:53:07,909 - INFO - app - app.py:230 - Successfully displayed monthly banking settlement for Kids Clinic India Limited
2025-07-08 15:53:08,132 - INFO - app - app.py:241 - Successfully displayed ToD generation analysis for Kids Clinic India Limited
2025-07-08 15:53:08,381 - INFO - app - app.py:252 - Successfully displayed ToD consumption analysis for Kids Clinic India Limited
2025-07-08 15:53:08,695 - INFO - app - app.py:263 - Successfully displayed hourly generation vs consumption chart for Kids Clinic India Limited
2025-07-08 15:53:08,696 - INFO - app - app.py:277 - ToD Analysis tab: Displaying data for Kids Clinic India Limited
2025-07-08 15:53:09,195 - INFO - app - app.py:294 - Successfully displayed ToD generation vs consumption for Kids Clinic India Limited
2025-07-08 15:53:09,195 - INFO - app - app.py:311 - Power Cost Analysis tab: Displaying data for Kids Clinic India Limited
2025-07-08 15:53:10,184 - INFO - app - app.py:316 - Successfully displayed power cost analysis for Kids Clinic India Limited
2025-07-08 16:03:08,948 - INFO - app - app.py:79 - Starting application initialization
2025-07-08 16:03:08,948 - INFO - app - app.py:81 - Page configuration completed
2025-07-08 16:03:08,948 - INFO - app - app.py:85 - Session state initialized
2025-07-08 16:03:08,948 - INFO - app - app.py:93 - User authenticated successfully
2025-07-08 16:03:08,948 - INFO - app - app.py:105 - Loading client data from database
2025-07-08 16:03:09,021 - INFO - app - app.py:114 - Successfully loaded data for 1 clients
2025-07-08 16:03:09,021 - INFO - app - app.py:62 - Successfully loaded image: logo/logo_integrum.jpg
2025-07-08 16:03:09,021 - INFO - app - app.py:145 - Client selection: Kids Clinic India Limited, Plant: None, Type: Combined
2025-07-08 16:03:09,036 - INFO - app - app.py:156 - Date range selected: 2025-05-01 to 2025-05-31
2025-07-08 16:03:09,041 - INFO - app - app.py:184 - Created main application tabs
2025-07-08 16:03:09,041 - INFO - app - app.py:194 - Summary tab: Displaying data for Kids Clinic India Limited
2025-07-08 16:03:09,556 - INFO - app - app.py:218 - Successfully displayed generation vs consumption chart for Kids Clinic India Limited
2025-07-08 16:03:10,558 - INFO - app - app.py:230 - Successfully displayed monthly banking settlement for Kids Clinic India Limited
2025-07-08 16:03:10,851 - INFO - app - app.py:241 - Successfully displayed ToD generation analysis for Kids Clinic India Limited
2025-07-08 16:03:11,129 - INFO - app - app.py:252 - Successfully displayed ToD consumption analysis for Kids Clinic India Limited
2025-07-08 16:03:11,426 - INFO - app - app.py:263 - Successfully displayed hourly generation vs consumption chart for Kids Clinic India Limited
2025-07-08 16:03:11,426 - INFO - app - app.py:277 - ToD Analysis tab: Displaying data for Kids Clinic India Limited
2025-07-08 16:03:11,943 - INFO - app - app.py:294 - Successfully displayed ToD generation vs consumption for Kids Clinic India Limited
2025-07-08 16:03:11,943 - INFO - app - app.py:311 - Power Cost Analysis tab: Displaying data for Kids Clinic India Limited
2025-07-08 16:03:12,929 - INFO - app - app.py:316 - Successfully displayed power cost analysis for Kids Clinic India Limited
2025-07-08 16:04:51,611 - INFO - app - app.py:79 - Starting application initialization
2025-07-08 16:04:51,611 - INFO - app - app.py:81 - Page configuration completed
2025-07-08 16:04:51,626 - INFO - app - app.py:85 - Session state initialized
2025-07-08 16:04:51,627 - INFO - app - app.py:93 - User authenticated successfully
2025-07-08 16:04:51,628 - INFO - app - app.py:105 - Loading client data from database
2025-07-08 16:04:51,769 - INFO - app - app.py:114 - Successfully loaded data for 1 clients
2025-07-08 16:04:51,769 - INFO - app - app.py:62 - Successfully loaded image: logo/logo_integrum.jpg
2025-07-08 16:04:51,769 - INFO - app - app.py:145 - Client selection: Kids Clinic India Limited, Plant: None, Type: Combined
2025-07-08 16:04:51,769 - INFO - app - app.py:156 - Date range selected: 2025-05-01 to 2025-05-31
2025-07-08 16:04:51,779 - INFO - app - app.py:184 - Created main application tabs
2025-07-08 16:04:51,783 - INFO - app - app.py:194 - Summary tab: Displaying data for Kids Clinic India Limited
2025-07-08 16:04:51,787 - ERROR - app - app.py:220 - Error displaying generation vs consumption: display_generation_vs_consumption() got an unexpected keyword argument 'is_hourly_aggregation'
2025-07-08 16:04:52,876 - INFO - app - app.py:230 - Successfully displayed monthly banking settlement for Kids Clinic India Limited
2025-07-08 16:04:53,199 - INFO - app - app.py:241 - Successfully displayed ToD generation analysis for Kids Clinic India Limited
2025-07-08 16:04:53,516 - INFO - app - app.py:252 - Successfully displayed ToD consumption analysis for Kids Clinic India Limited
2025-07-08 16:04:53,811 - INFO - app - app.py:263 - Successfully displayed hourly generation vs consumption chart for Kids Clinic India Limited
2025-07-08 16:04:53,812 - INFO - app - app.py:277 - ToD Analysis tab: Displaying data for Kids Clinic India Limited
2025-07-08 16:04:54,287 - INFO - app - app.py:294 - Successfully displayed ToD generation vs consumption for Kids Clinic India Limited
2025-07-08 16:04:54,289 - INFO - app - app.py:311 - Power Cost Analysis tab: Displaying data for Kids Clinic India Limited
2025-07-08 16:04:55,321 - INFO - app - app.py:316 - Successfully displayed power cost analysis for Kids Clinic India Limited
2025-07-08 16:05:24,473 - INFO - app - app.py:79 - Starting application initialization
2025-07-08 16:05:24,473 - INFO - app - app.py:81 - Page configuration completed
2025-07-08 16:05:24,474 - INFO - app - app.py:85 - Session state initialized
2025-07-08 16:05:24,474 - INFO - app - app.py:93 - User authenticated successfully
2025-07-08 16:05:24,476 - INFO - app - app.py:105 - Loading client data from database
2025-07-08 16:05:24,584 - INFO - app - app.py:114 - Successfully loaded data for 1 clients
2025-07-08 16:05:24,584 - INFO - app - app.py:62 - Successfully loaded image: logo/logo_integrum.jpg
2025-07-08 16:05:24,594 - INFO - app - app.py:145 - Client selection: Kids Clinic India Limited, Plant: None, Type: Combined
2025-07-08 16:05:24,596 - INFO - app - app.py:156 - Date range selected: 2025-05-01 to 2025-05-31
2025-07-08 16:05:24,599 - INFO - app - app.py:184 - Created main application tabs
2025-07-08 16:05:24,599 - INFO - app - app.py:194 - Summary tab: Displaying data for Kids Clinic India Limited
2025-07-08 16:05:24,601 - ERROR - app - app.py:263 - Error in Summary tab: name 'c' is not defined
2025-07-08 16:05:24,602 - INFO - app - app.py:268 - ToD Analysis tab: Displaying data for Kids Clinic India Limited
2025-07-08 16:05:25,195 - INFO - app - app.py:285 - Successfully displayed ToD generation vs consumption for Kids Clinic India Limited
2025-07-08 16:05:25,196 - INFO - app - app.py:302 - Power Cost Analysis tab: Displaying data for Kids Clinic India Limited
2025-07-08 16:05:26,303 - INFO - app - app.py:307 - Successfully displayed power cost analysis for Kids Clinic India Limited
2025-07-08 16:05:48,454 - INFO - app - app.py:79 - Starting application initialization
2025-07-08 16:05:48,454 - INFO - app - app.py:81 - Page configuration completed
2025-07-08 16:05:48,454 - INFO - app - app.py:85 - Session state initialized
2025-07-08 16:05:48,454 - INFO - app - app.py:93 - User authenticated successfully
2025-07-08 16:05:48,454 - INFO - app - app.py:105 - Loading client data from database
2025-07-08 16:05:48,558 - INFO - app - app.py:114 - Successfully loaded data for 1 clients
2025-07-08 16:05:48,560 - INFO - app - app.py:62 - Successfully loaded image: logo/logo_integrum.jpg
2025-07-08 16:05:48,562 - INFO - app - app.py:145 - Client selection: Kids Clinic India Limited, Plant: None, Type: Combined
2025-07-08 16:05:48,563 - INFO - app - app.py:156 - Date range selected: 2025-05-01 to 2025-05-31
2025-07-08 16:05:48,567 - INFO - app - app.py:184 - Created main application tabs
2025-07-08 16:05:48,568 - INFO - app - app.py:194 - Summary tab: Displaying data for Kids Clinic India Limited
2025-07-08 16:05:48,712 - INFO - app - app.py:209 - Successfully displayed generation vs consumption chart for Kids Clinic India Limited
2025-07-08 16:05:49,705 - INFO - app - app.py:221 - Successfully displayed monthly banking settlement for Kids Clinic India Limited
2025-07-08 16:05:49,957 - INFO - app - app.py:232 - Successfully displayed ToD generation analysis for Kids Clinic India Limited
2025-07-08 16:05:50,236 - INFO - app - app.py:243 - Successfully displayed ToD consumption analysis for Kids Clinic India Limited
2025-07-08 16:05:50,550 - INFO - app - app.py:254 - Successfully displayed hourly generation vs consumption chart for Kids Clinic India Limited
2025-07-08 16:05:50,551 - INFO - app - app.py:268 - ToD Analysis tab: Displaying data for Kids Clinic India Limited
2025-07-08 16:05:51,132 - INFO - app - app.py:285 - Successfully displayed ToD generation vs consumption for Kids Clinic India Limited
2025-07-08 16:05:51,133 - INFO - app - app.py:302 - Power Cost Analysis tab: Displaying data for Kids Clinic India Limited
2025-07-08 16:05:52,204 - INFO - app - app.py:307 - Successfully displayed power cost analysis for Kids Clinic India Limited
2025-07-08 16:07:16,954 - INFO - app - app.py:79 - Starting application initialization
2025-07-08 16:07:16,955 - INFO - app - app.py:81 - Page configuration completed
2025-07-08 16:07:16,955 - INFO - app - app.py:85 - Session state initialized
2025-07-08 16:07:16,956 - INFO - app - app.py:93 - User authenticated successfully
2025-07-08 16:07:16,958 - INFO - app - app.py:105 - Loading client data from database
2025-07-08 16:07:17,055 - INFO - app - app.py:114 - Successfully loaded data for 1 clients
2025-07-08 16:07:17,055 - INFO - app - app.py:62 - Successfully loaded image: logo/logo_integrum.jpg
2025-07-08 16:07:17,055 - INFO - app - app.py:145 - Client selection: Kids Clinic India Limited, Plant: None, Type: Combined
2025-07-08 16:07:17,055 - INFO - app - app.py:156 - Date range selected: 2025-05-01 to 2025-05-31
2025-07-08 16:07:17,063 - INFO - app - app.py:184 - Created main application tabs
2025-07-08 16:07:17,064 - INFO - app - app.py:194 - Summary tab: Displaying data for Kids Clinic India Limited
2025-07-08 16:07:17,173 - INFO - app - app.py:209 - Successfully displayed generation vs consumption chart for Kids Clinic India Limited
2025-07-08 16:07:18,261 - INFO - app - app.py:221 - Successfully displayed monthly banking settlement for Kids Clinic India Limited
2025-07-08 16:07:18,514 - INFO - app - app.py:232 - Successfully displayed ToD generation analysis for Kids Clinic India Limited
2025-07-08 16:07:18,765 - INFO - app - app.py:243 - Successfully displayed ToD consumption analysis for Kids Clinic India Limited
2025-07-08 16:07:19,095 - INFO - app - app.py:254 - Successfully displayed hourly generation vs consumption chart for Kids Clinic India Limited
2025-07-08 16:07:19,095 - INFO - app - app.py:268 - ToD Analysis tab: Displaying data for Kids Clinic India Limited
2025-07-08 16:07:19,639 - INFO - app - app.py:285 - Successfully displayed ToD generation vs consumption for Kids Clinic India Limited
2025-07-08 16:07:19,639 - INFO - app - app.py:302 - Power Cost Analysis tab: Displaying data for Kids Clinic India Limited
2025-07-08 16:07:20,601 - INFO - app - app.py:307 - Successfully displayed power cost analysis for Kids Clinic India Limited
2025-07-08 16:07:48,690 - INFO - app - app.py:79 - Starting application initialization
2025-07-08 16:07:48,692 - INFO - app - app.py:81 - Page configuration completed
2025-07-08 16:07:48,692 - INFO - app - app.py:85 - Session state initialized
2025-07-08 16:07:48,693 - INFO - app - app.py:93 - User authenticated successfully
2025-07-08 16:07:48,694 - INFO - app - app.py:105 - Loading client data from database
2025-07-08 16:07:48,819 - INFO - app - app.py:114 - Successfully loaded data for 1 clients
2025-07-08 16:07:48,819 - INFO - app - app.py:62 - Successfully loaded image: logo/logo_integrum.jpg
2025-07-08 16:07:48,822 - INFO - app - app.py:145 - Client selection: Kids Clinic India Limited, Plant: None, Type: Combined
2025-07-08 16:07:48,824 - INFO - app - app.py:156 - Date range selected: 2025-05-01 to 2025-05-31
2025-07-08 16:07:48,827 - INFO - app - app.py:184 - Created main application tabs
2025-07-08 16:07:48,828 - INFO - app - app.py:194 - Summary tab: Displaying data for Kids Clinic India Limited
2025-07-08 16:07:48,942 - INFO - app - app.py:209 - Successfully displayed generation vs consumption chart for Kids Clinic India Limited
2025-07-08 16:07:50,000 - INFO - app - app.py:221 - Successfully displayed monthly banking settlement for Kids Clinic India Limited
2025-07-08 16:07:50,278 - INFO - app - app.py:232 - Successfully displayed ToD generation analysis for Kids Clinic India Limited
2025-07-08 16:07:50,686 - INFO - app - app.py:243 - Successfully displayed ToD consumption analysis for Kids Clinic India Limited
2025-07-08 16:07:51,130 - INFO - app - app.py:254 - Successfully displayed hourly generation vs consumption chart for Kids Clinic India Limited
2025-07-08 16:07:51,130 - INFO - app - app.py:268 - ToD Analysis tab: Displaying data for Kids Clinic India Limited
2025-07-08 16:07:51,653 - INFO - app - app.py:285 - Successfully displayed ToD generation vs consumption for Kids Clinic India Limited
2025-07-08 16:07:51,669 - INFO - app - app.py:302 - Power Cost Analysis tab: Displaying data for Kids Clinic India Limited
2025-07-08 16:07:52,793 - INFO - app - app.py:79 - Starting application initialization
2025-07-08 16:07:52,793 - INFO - app - app.py:81 - Page configuration completed
2025-07-08 16:07:52,793 - INFO - app - app.py:85 - Session state initialized
2025-07-08 16:07:52,793 - INFO - app - app.py:93 - User authenticated successfully
2025-07-08 16:07:52,793 - INFO - app - app.py:105 - Loading client data from database
2025-07-08 16:07:52,913 - INFO - app - app.py:114 - Successfully loaded data for 1 clients
2025-07-08 16:07:52,913 - INFO - app - app.py:62 - Successfully loaded image: logo/logo_integrum.jpg
2025-07-08 16:07:52,916 - INFO - app - app.py:145 - Client selection: Kids Clinic India Limited, Plant: None, Type: Combined
2025-07-08 16:07:52,916 - INFO - app - app.py:156 - Date range selected: 2025-05-01 to 2025-05-31
2025-07-08 16:07:52,920 - INFO - app - app.py:184 - Created main application tabs
2025-07-08 16:07:52,920 - INFO - app - app.py:194 - Summary tab: Displaying data for Kids Clinic India Limited
2025-07-08 16:07:53,028 - INFO - app - app.py:209 - Successfully displayed generation vs consumption chart for Kids Clinic India Limited
2025-07-08 16:07:54,062 - INFO - app - app.py:221 - Successfully displayed monthly banking settlement for Kids Clinic India Limited
2025-07-08 16:07:54,312 - INFO - app - app.py:232 - Successfully displayed ToD generation analysis for Kids Clinic India Limited
2025-07-08 16:07:54,546 - INFO - app - app.py:243 - Successfully displayed ToD consumption analysis for Kids Clinic India Limited
2025-07-08 16:07:54,831 - INFO - app - app.py:254 - Successfully displayed hourly generation vs consumption chart for Kids Clinic India Limited
2025-07-08 16:07:54,831 - INFO - app - app.py:268 - ToD Analysis tab: Displaying data for Kids Clinic India Limited
2025-07-08 16:07:55,375 - INFO - app - app.py:285 - Successfully displayed ToD generation vs consumption for Kids Clinic India Limited
2025-07-08 16:07:55,375 - INFO - app - app.py:302 - Power Cost Analysis tab: Displaying data for Kids Clinic India Limited
2025-07-08 16:07:56,443 - INFO - app - app.py:307 - Successfully displayed power cost analysis for Kids Clinic India Limited
2025-07-08 16:08:29,127 - INFO - app - app.py:79 - Starting application initialization
2025-07-08 16:08:29,127 - INFO - app - app.py:81 - Page configuration completed
2025-07-08 16:08:29,135 - INFO - app - app.py:85 - Session state initialized
2025-07-08 16:08:29,136 - INFO - app - app.py:93 - User authenticated successfully
2025-07-08 16:08:29,137 - INFO - app - app.py:105 - Loading client data from database
2025-07-08 16:08:29,220 - INFO - app - app.py:114 - Successfully loaded data for 1 clients
2025-07-08 16:08:29,221 - INFO - app - app.py:62 - Successfully loaded image: logo/logo_integrum.jpg
2025-07-08 16:08:29,223 - INFO - app - app.py:145 - Client selection: Kids Clinic India Limited, Plant: None, Type: Combined
2025-07-08 16:08:29,224 - INFO - app - app.py:156 - Date range selected: 2025-05-01 to 2025-05-31
2025-07-08 16:08:29,228 - INFO - app - app.py:184 - Created main application tabs
2025-07-08 16:08:29,229 - INFO - app - app.py:194 - Summary tab: Displaying data for Kids Clinic India Limited
2025-07-08 16:08:29,331 - INFO - app - app.py:209 - Successfully displayed generation vs consumption chart for Kids Clinic India Limited
2025-07-08 16:08:30,322 - INFO - app - app.py:221 - Successfully displayed monthly banking settlement for Kids Clinic India Limited
2025-07-08 16:08:30,545 - INFO - app - app.py:232 - Successfully displayed ToD generation analysis for Kids Clinic India Limited
2025-07-08 16:08:30,813 - INFO - app - app.py:243 - Successfully displayed ToD consumption analysis for Kids Clinic India Limited
2025-07-08 16:08:31,065 - INFO - app - app.py:254 - Successfully displayed hourly generation vs consumption chart for Kids Clinic India Limited
2025-07-08 16:08:31,065 - INFO - app - app.py:268 - ToD Analysis tab: Displaying data for Kids Clinic India Limited
2025-07-08 16:08:31,532 - INFO - app - app.py:285 - Successfully displayed ToD generation vs consumption for Kids Clinic India Limited
2025-07-08 16:09:42,300 - INFO - app - app.py:79 - Starting application initialization
2025-07-08 16:09:42,301 - INFO - app - app.py:81 - Page configuration completed
2025-07-08 16:09:42,302 - INFO - app - app.py:85 - Session state initialized
2025-07-08 16:09:42,302 - INFO - app - app.py:93 - User authenticated successfully
2025-07-08 16:09:42,303 - INFO - app - app.py:105 - Loading client data from database
2025-07-08 16:09:42,418 - INFO - app - app.py:114 - Successfully loaded data for 1 clients
2025-07-08 16:09:42,419 - INFO - app - app.py:62 - Successfully loaded image: logo/logo_integrum.jpg
2025-07-08 16:09:42,423 - INFO - app - app.py:145 - Client selection: Kids Clinic India Limited, Plant: None, Type: Combined
2025-07-08 16:09:42,425 - INFO - app - app.py:156 - Date range selected: 2025-05-01 to 2025-05-31
2025-07-08 16:09:42,429 - INFO - app - app.py:184 - Created main application tabs
2025-07-08 16:09:42,429 - INFO - app - app.py:194 - Summary tab: Displaying data for Kids Clinic India Limited
2025-07-08 16:09:42,544 - INFO - app - app.py:209 - Successfully displayed generation vs consumption chart for Kids Clinic India Limited
2025-07-08 16:09:43,610 - INFO - app - app.py:221 - Successfully displayed monthly banking settlement for Kids Clinic India Limited
2025-07-08 16:09:43,886 - INFO - app - app.py:232 - Successfully displayed ToD generation analysis for Kids Clinic India Limited
2025-07-08 16:09:44,158 - INFO - app - app.py:243 - Successfully displayed ToD consumption analysis for Kids Clinic India Limited
2025-07-08 16:09:44,494 - INFO - app - app.py:254 - Successfully displayed hourly generation vs consumption chart for Kids Clinic India Limited
2025-07-08 16:09:44,494 - INFO - app - app.py:268 - ToD Analysis tab: Displaying data for Kids Clinic India Limited
2025-07-08 16:09:44,976 - INFO - app - app.py:285 - Successfully displayed ToD generation vs consumption for Kids Clinic India Limited
2025-07-08 16:10:36,893 - INFO - app - app.py:79 - Starting application initialization
2025-07-08 16:10:36,893 - INFO - app - app.py:81 - Page configuration completed
2025-07-08 16:10:36,896 - INFO - app - app.py:85 - Session state initialized
2025-07-08 16:10:36,897 - INFO - app - app.py:93 - User authenticated successfully
2025-07-08 16:10:36,898 - INFO - app - app.py:105 - Loading client data from database
2025-07-08 16:10:37,036 - INFO - app - app.py:114 - Successfully loaded data for 1 clients
2025-07-08 16:10:37,036 - INFO - app - app.py:62 - Successfully loaded image: logo/logo_integrum.jpg
2025-07-08 16:10:37,036 - INFO - app - app.py:145 - Client selection: Kids Clinic India Limited, Plant: None, Type: Combined
2025-07-08 16:10:37,036 - INFO - app - app.py:156 - Date range selected: 2025-05-01 to 2025-05-31
2025-07-08 16:10:37,036 - INFO - app - app.py:184 - Created main application tabs
2025-07-08 16:10:37,036 - INFO - app - app.py:194 - Summary tab: Displaying data for Kids Clinic India Limited
2025-07-08 16:10:37,153 - INFO - app - app.py:209 - Successfully displayed generation vs consumption chart for Kids Clinic India Limited
2025-07-08 16:10:38,192 - INFO - app - app.py:221 - Successfully displayed monthly banking settlement for Kids Clinic India Limited
2025-07-08 16:10:38,469 - INFO - app - app.py:232 - Successfully displayed ToD generation analysis for Kids Clinic India Limited
2025-07-08 16:10:38,739 - INFO - app - app.py:243 - Successfully displayed ToD consumption analysis for Kids Clinic India Limited
2025-07-08 16:10:39,103 - INFO - app - app.py:254 - Successfully displayed hourly generation vs consumption chart for Kids Clinic India Limited
2025-07-08 16:10:39,103 - INFO - app - app.py:268 - ToD Analysis tab: Displaying data for Kids Clinic India Limited
2025-07-08 16:10:39,622 - INFO - app - app.py:285 - Successfully displayed ToD generation vs consumption for Kids Clinic India Limited
2025-07-08 16:12:02,849 - INFO - app - app.py:79 - Starting application initialization
2025-07-08 16:12:02,850 - INFO - app - app.py:81 - Page configuration completed
2025-07-08 16:12:02,850 - INFO - app - app.py:85 - Session state initialized
2025-07-08 16:12:02,850 - INFO - app - app.py:93 - User authenticated successfully
2025-07-08 16:12:02,850 - INFO - app - app.py:105 - Loading client data from database
2025-07-08 16:12:02,951 - INFO - app - app.py:114 - Successfully loaded data for 1 clients
2025-07-08 16:12:02,952 - INFO - app - app.py:62 - Successfully loaded image: logo/logo_integrum.jpg
2025-07-08 16:12:02,954 - INFO - app - app.py:145 - Client selection: Kids Clinic India Limited, Plant: None, Type: Combined
2025-07-08 16:12:02,955 - INFO - app - app.py:156 - Date range selected: 2025-05-01 to 2025-05-31
2025-07-08 16:12:02,958 - INFO - app - app.py:184 - Created main application tabs
2025-07-08 16:12:02,959 - INFO - app - app.py:194 - Summary tab: Displaying data for Kids Clinic India Limited
2025-07-08 16:12:03,077 - INFO - app - app.py:209 - Successfully displayed generation vs consumption chart for Kids Clinic India Limited
2025-07-08 16:12:03,077 - INFO - app - app.py:268 - ToD Analysis tab: Displaying data for Kids Clinic India Limited
2025-07-08 16:12:35,467 - INFO - app - app.py:79 - Starting application initialization
2025-07-08 16:12:35,468 - INFO - app - app.py:81 - Page configuration completed
2025-07-08 16:12:35,468 - INFO - app - app.py:85 - Session state initialized
2025-07-08 16:12:35,468 - INFO - app - app.py:93 - User authenticated successfully
2025-07-08 16:12:35,468 - INFO - app - app.py:105 - Loading client data from database
2025-07-08 16:12:35,563 - INFO - app - app.py:114 - Successfully loaded data for 1 clients
2025-07-08 16:12:35,563 - INFO - app - app.py:62 - Successfully loaded image: logo/logo_integrum.jpg
2025-07-08 16:12:35,563 - INFO - app - app.py:145 - Client selection: Kids Clinic India Limited, Plant: None, Type: Combined
2025-07-08 16:12:35,563 - INFO - app - app.py:156 - Date range selected: 2025-05-01 to 2025-05-31
2025-07-08 16:12:35,571 - INFO - app - app.py:184 - Created main application tabs
2025-07-08 16:12:35,571 - INFO - app - app.py:194 - Summary tab: Displaying data for Kids Clinic India Limited
2025-07-08 16:12:35,946 - INFO - app - app.py:209 - Successfully displayed generation vs consumption chart for Kids Clinic India Limited
2025-07-08 16:12:35,946 - INFO - app - app.py:268 - ToD Analysis tab: Displaying data for Kids Clinic India Limited
2025-07-08 16:14:51,301 - INFO - app - app.py:79 - Starting application initialization
2025-07-08 16:14:51,303 - INFO - app - app.py:81 - Page configuration completed
2025-07-08 16:14:51,305 - INFO - app - app.py:85 - Session state initialized
2025-07-08 16:14:51,305 - INFO - app - app.py:93 - User authenticated successfully
2025-07-08 16:14:51,307 - INFO - app - app.py:105 - Loading client data from database
2025-07-08 16:14:51,499 - INFO - app - app.py:114 - Successfully loaded data for 1 clients
2025-07-08 16:14:51,499 - INFO - app - app.py:62 - Successfully loaded image: logo/logo_integrum.jpg
2025-07-08 16:14:51,499 - INFO - app - app.py:145 - Client selection: Kids Clinic India Limited, Plant: None, Type: Combined
2025-07-08 16:14:51,499 - INFO - app - app.py:156 - Date range selected: 2025-05-01 to 2025-05-31
2025-07-08 16:14:51,499 - INFO - app - app.py:184 - Created main application tabs
2025-07-08 16:14:51,515 - INFO - app - app.py:194 - Summary tab: Displaying data for Kids Clinic India Limited
2025-07-08 16:14:51,942 - INFO - app - app.py:209 - Successfully displayed generation vs consumption chart for Kids Clinic India Limited
2025-07-08 16:14:51,942 - INFO - app - app.py:268 - ToD Analysis tab: Displaying data for Kids Clinic India Limited
2025-07-08 16:15:26,508 - INFO - app - app.py:79 - Starting application initialization
2025-07-08 16:15:26,508 - INFO - app - app.py:81 - Page configuration completed
2025-07-08 16:15:26,516 - INFO - app - app.py:85 - Session state initialized
2025-07-08 16:15:26,516 - INFO - app - app.py:93 - User authenticated successfully
2025-07-08 16:15:26,517 - INFO - app - app.py:105 - Loading client data from database
2025-07-08 16:15:26,636 - INFO - app - app.py:114 - Successfully loaded data for 1 clients
2025-07-08 16:15:26,637 - INFO - app - app.py:62 - Successfully loaded image: logo/logo_integrum.jpg
2025-07-08 16:15:26,639 - INFO - app - app.py:145 - Client selection: Kids Clinic India Limited, Plant: None, Type: Combined
2025-07-08 16:15:26,640 - INFO - app - app.py:156 - Date range selected: 2025-05-01 to 2025-05-31
2025-07-08 16:15:26,644 - INFO - app - app.py:184 - Created main application tabs
2025-07-08 16:15:26,644 - INFO - app - app.py:194 - Summary tab: Displaying data for Kids Clinic India Limited
2025-07-08 16:15:26,647 - ERROR - app - app.py:211 - Error displaying generation vs consumption: cannot access local variable 'df' where it is not associated with a value
2025-07-08 16:15:26,648 - INFO - app - app.py:268 - ToD Analysis tab: Displaying data for Kids Clinic India Limited
2025-07-08 16:15:46,849 - INFO - app - app.py:79 - Starting application initialization
2025-07-08 16:15:46,849 - INFO - app - app.py:81 - Page configuration completed
2025-07-08 16:15:46,849 - INFO - app - app.py:85 - Session state initialized
2025-07-08 16:15:46,849 - INFO - app - app.py:93 - User authenticated successfully
2025-07-08 16:15:46,849 - INFO - app - app.py:105 - Loading client data from database
2025-07-08 16:15:46,944 - INFO - app - app.py:114 - Successfully loaded data for 1 clients
2025-07-08 16:15:46,945 - INFO - app - app.py:62 - Successfully loaded image: logo/logo_integrum.jpg
2025-07-08 16:15:46,946 - INFO - app - app.py:145 - Client selection: Kids Clinic India Limited, Plant: None, Type: Combined
2025-07-08 16:15:46,947 - INFO - app - app.py:156 - Date range selected: 2025-05-01 to 2025-05-31
2025-07-08 16:15:46,950 - INFO - app - app.py:184 - Created main application tabs
2025-07-08 16:15:46,951 - INFO - app - app.py:194 - Summary tab: Displaying data for Kids Clinic India Limited
2025-07-08 16:15:47,312 - INFO - app - app.py:209 - Successfully displayed generation vs consumption chart for Kids Clinic India Limited
2025-07-08 16:15:47,312 - INFO - app - app.py:268 - ToD Analysis tab: Displaying data for Kids Clinic India Limited
2025-07-08 16:16:42,098 - INFO - app - app.py:79 - Starting application initialization
2025-07-08 16:16:42,099 - INFO - app - app.py:81 - Page configuration completed
2025-07-08 16:16:42,100 - INFO - app - app.py:85 - Session state initialized
2025-07-08 16:16:42,100 - INFO - app - app.py:93 - User authenticated successfully
2025-07-08 16:16:42,101 - INFO - app - app.py:105 - Loading client data from database
2025-07-08 16:16:42,209 - INFO - app - app.py:114 - Successfully loaded data for 1 clients
2025-07-08 16:16:42,210 - INFO - app - app.py:62 - Successfully loaded image: logo/logo_integrum.jpg
2025-07-08 16:16:42,211 - INFO - app - app.py:145 - Client selection: Kids Clinic India Limited, Plant: None, Type: Combined
2025-07-08 16:16:42,214 - INFO - app - app.py:156 - Date range selected: 2025-05-01 to 2025-05-31
2025-07-08 16:16:42,218 - INFO - app - app.py:184 - Created main application tabs
2025-07-08 16:16:42,218 - INFO - app - app.py:194 - Summary tab: Displaying data for Kids Clinic India Limited
2025-07-08 16:16:42,615 - INFO - app - app.py:209 - Successfully displayed generation vs consumption chart for Kids Clinic India Limited
2025-07-08 16:16:42,615 - INFO - app - app.py:268 - ToD Analysis tab: Displaying data for Kids Clinic India Limited
2025-07-08 16:16:58,878 - INFO - app - app.py:79 - Starting application initialization
2025-07-08 16:16:58,879 - INFO - app - app.py:81 - Page configuration completed
2025-07-08 16:16:58,879 - INFO - app - app.py:85 - Session state initialized
2025-07-08 16:16:58,879 - INFO - app - app.py:93 - User authenticated successfully
2025-07-08 16:16:58,882 - INFO - app - app.py:105 - Loading client data from database
2025-07-08 16:16:59,015 - INFO - app - app.py:114 - Successfully loaded data for 1 clients
2025-07-08 16:16:59,016 - INFO - app - app.py:62 - Successfully loaded image: logo/logo_integrum.jpg
2025-07-08 16:16:59,017 - INFO - app - app.py:145 - Client selection: Kids Clinic India Limited, Plant: None, Type: Combined
2025-07-08 16:16:59,020 - INFO - app - app.py:156 - Date range selected: 2025-05-01 to 2025-05-31
2025-07-08 16:16:59,024 - INFO - app - app.py:184 - Created main application tabs
2025-07-08 16:16:59,024 - INFO - app - app.py:194 - Summary tab: Displaying data for Kids Clinic India Limited
2025-07-08 16:16:59,028 - INFO - app - app.py:209 - Successfully displayed generation vs consumption chart for Kids Clinic India Limited
2025-07-08 16:16:59,029 - INFO - app - app.py:268 - ToD Analysis tab: Displaying data for Kids Clinic India Limited
2025-07-08 16:17:09,044 - INFO - app - app.py:79 - Starting application initialization
2025-07-08 16:17:09,046 - INFO - app - app.py:81 - Page configuration completed
2025-07-08 16:17:09,046 - INFO - app - app.py:85 - Session state initialized
2025-07-08 16:17:09,046 - INFO - app - app.py:93 - User authenticated successfully
2025-07-08 16:17:09,048 - INFO - app - app.py:105 - Loading client data from database
2025-07-08 16:17:09,134 - INFO - app - app.py:114 - Successfully loaded data for 1 clients
2025-07-08 16:17:09,134 - INFO - app - app.py:62 - Successfully loaded image: logo/logo_integrum.jpg
2025-07-08 16:17:09,149 - INFO - app - app.py:145 - Client selection: Kids Clinic India Limited, Plant: None, Type: Combined
2025-07-08 16:17:09,154 - INFO - app - app.py:156 - Date range selected: 2025-05-01 to 2025-05-31
2025-07-08 16:17:09,155 - INFO - app - app.py:184 - Created main application tabs
2025-07-08 16:17:09,156 - INFO - app - app.py:194 - Summary tab: Displaying data for Kids Clinic India Limited
2025-07-08 16:17:09,585 - INFO - app - app.py:209 - Successfully displayed generation vs consumption chart for Kids Clinic India Limited
2025-07-08 16:17:09,585 - INFO - app - app.py:268 - ToD Analysis tab: Displaying data for Kids Clinic India Limited
2025-07-08 16:18:20,121 - INFO - app - app.py:79 - Starting application initialization
2025-07-08 16:18:20,121 - INFO - app - app.py:81 - Page configuration completed
2025-07-08 16:18:20,132 - INFO - app - app.py:85 - Session state initialized
2025-07-08 16:18:20,133 - INFO - app - app.py:93 - User authenticated successfully
2025-07-08 16:18:20,134 - INFO - app - app.py:105 - Loading client data from database
2025-07-08 16:18:20,215 - INFO - app - app.py:114 - Successfully loaded data for 1 clients
2025-07-08 16:18:20,216 - INFO - app - app.py:62 - Successfully loaded image: logo/logo_integrum.jpg
2025-07-08 16:18:20,218 - INFO - app - app.py:145 - Client selection: Kids Clinic India Limited, Plant: None, Type: Combined
2025-07-08 16:18:20,219 - INFO - app - app.py:156 - Date range selected: 2025-05-01 to 2025-05-31
2025-07-08 16:18:20,223 - INFO - app - app.py:184 - Created main application tabs
2025-07-08 16:18:20,223 - INFO - app - app.py:194 - Summary tab: Displaying data for Kids Clinic India Limited
2025-07-08 16:18:20,643 - INFO - app - app.py:209 - Successfully displayed generation vs consumption chart for Kids Clinic India Limited
2025-07-08 16:18:20,645 - INFO - app - app.py:268 - ToD Analysis tab: Displaying data for Kids Clinic India Limited
2025-07-08 16:19:02,310 - INFO - app - app.py:79 - Starting application initialization
2025-07-08 16:19:02,312 - INFO - app - app.py:81 - Page configuration completed
2025-07-08 16:19:02,312 - INFO - app - app.py:85 - Session state initialized
2025-07-08 16:19:02,312 - INFO - app - app.py:93 - User authenticated successfully
2025-07-08 16:19:02,315 - INFO - app - app.py:105 - Loading client data from database
2025-07-08 16:19:02,427 - INFO - app - app.py:114 - Successfully loaded data for 1 clients
2025-07-08 16:19:02,428 - INFO - app - app.py:62 - Successfully loaded image: logo/logo_integrum.jpg
2025-07-08 16:19:02,430 - INFO - app - app.py:145 - Client selection: Kids Clinic India Limited, Plant: None, Type: Combined
2025-07-08 16:19:02,430 - INFO - app - app.py:156 - Date range selected: 2025-05-01 to 2025-05-31
2025-07-08 16:19:02,435 - INFO - app - app.py:184 - Created main application tabs
2025-07-08 16:19:02,435 - INFO - app - app.py:194 - Summary tab: Displaying data for Kids Clinic India Limited
2025-07-08 16:19:02,726 - INFO - app - app.py:209 - Successfully displayed generation vs consumption chart for Kids Clinic India Limited
2025-07-08 16:19:02,726 - INFO - app - app.py:268 - ToD Analysis tab: Displaying data for Kids Clinic India Limited
2025-07-08 16:19:20,134 - INFO - app - app.py:79 - Starting application initialization
2025-07-08 16:19:20,142 - INFO - app - app.py:81 - Page configuration completed
2025-07-08 16:19:20,143 - INFO - app - app.py:85 - Session state initialized
2025-07-08 16:19:20,144 - INFO - app - app.py:93 - User authenticated successfully
2025-07-08 16:19:20,144 - INFO - app - app.py:105 - Loading client data from database
2025-07-08 16:19:20,245 - INFO - app - app.py:114 - Successfully loaded data for 1 clients
2025-07-08 16:19:20,246 - INFO - app - app.py:62 - Successfully loaded image: logo/logo_integrum.jpg
2025-07-08 16:19:20,248 - INFO - app - app.py:145 - Client selection: Kids Clinic India Limited, Plant: None, Type: Combined
2025-07-08 16:19:20,249 - INFO - app - app.py:156 - Date range selected: 2025-05-01 to 2025-05-31
2025-07-08 16:19:20,254 - INFO - app - app.py:184 - Created main application tabs
2025-07-08 16:19:20,254 - INFO - app - app.py:194 - Summary tab: Displaying data for Kids Clinic India Limited
2025-07-08 16:19:20,565 - INFO - app - app.py:209 - Successfully displayed generation vs consumption chart for Kids Clinic India Limited
2025-07-08 16:19:20,566 - INFO - app - app.py:268 - ToD Analysis tab: Displaying data for Kids Clinic India Limited
2025-07-08 16:19:36,461 - INFO - app - app.py:79 - Starting application initialization
2025-07-08 16:19:36,461 - INFO - app - app.py:81 - Page configuration completed
2025-07-08 16:19:36,461 - INFO - app - app.py:85 - Session state initialized
2025-07-08 16:19:36,461 - INFO - app - app.py:93 - User authenticated successfully
2025-07-08 16:19:36,461 - INFO - app - app.py:105 - Loading client data from database
2025-07-08 16:19:36,585 - INFO - app - app.py:114 - Successfully loaded data for 1 clients
2025-07-08 16:19:36,587 - INFO - app - app.py:62 - Successfully loaded image: logo/logo_integrum.jpg
2025-07-08 16:19:36,589 - INFO - app - app.py:145 - Client selection: Kids Clinic India Limited, Plant: None, Type: Combined
2025-07-08 16:19:36,591 - INFO - app - app.py:156 - Date range selected: 2025-05-01 to 2025-05-31
2025-07-08 16:19:36,594 - INFO - app - app.py:184 - Created main application tabs
2025-07-08 16:19:36,594 - INFO - app - app.py:194 - Summary tab: Displaying data for Kids Clinic India Limited
2025-07-08 16:19:36,969 - INFO - app - app.py:209 - Successfully displayed generation vs consumption chart for Kids Clinic India Limited
2025-07-08 16:19:36,969 - INFO - app - app.py:268 - ToD Analysis tab: Displaying data for Kids Clinic India Limited
2025-07-08 16:20:41,047 - INFO - app - app.py:79 - Starting application initialization
2025-07-08 16:20:41,049 - INFO - app - app.py:81 - Page configuration completed
2025-07-08 16:20:41,050 - INFO - app - app.py:85 - Session state initialized
2025-07-08 16:20:41,050 - INFO - app - app.py:93 - User authenticated successfully
2025-07-08 16:20:41,051 - INFO - app - app.py:105 - Loading client data from database
2025-07-08 16:20:41,142 - INFO - app - app.py:114 - Successfully loaded data for 1 clients
2025-07-08 16:20:41,143 - INFO - app - app.py:62 - Successfully loaded image: logo/logo_integrum.jpg
2025-07-08 16:20:41,144 - INFO - app - app.py:145 - Client selection: Kids Clinic India Limited, Plant: None, Type: Combined
2025-07-08 16:20:41,146 - INFO - app - app.py:156 - Date range selected: 2025-05-01 to 2025-05-31
2025-07-08 16:20:41,151 - INFO - app - app.py:184 - Created main application tabs
2025-07-08 16:20:41,151 - INFO - app - app.py:194 - Summary tab: Displaying data for Kids Clinic India Limited
2025-07-08 16:20:41,700 - INFO - app - app.py:218 - Successfully displayed generation vs consumption chart for Kids Clinic India Limited
2025-07-08 16:20:41,700 - INFO - app - app.py:275 - ToD Analysis tab: Displaying data for Kids Clinic India Limited
2025-07-08 16:29:51,889 - INFO - app - app.py:79 - Starting application initialization
2025-07-08 16:29:51,940 - INFO - app - app.py:81 - Page configuration completed
2025-07-08 16:29:51,940 - INFO - app - app.py:85 - Session state initialized
2025-07-08 16:29:51,940 - INFO - app - app.py:89 - User not authenticated, showing login form
2025-07-08 16:29:56,590 - INFO - app - app.py:79 - Starting application initialization
2025-07-08 16:29:56,590 - INFO - app - app.py:81 - Page configuration completed
2025-07-08 16:29:56,590 - INFO - app - app.py:85 - Session state initialized
2025-07-08 16:29:56,590 - INFO - app - app.py:89 - User not authenticated, showing login form
2025-07-08 16:29:58,193 - INFO - app - app.py:79 - Starting application initialization
2025-07-08 16:29:58,193 - INFO - app - app.py:81 - Page configuration completed
2025-07-08 16:29:58,209 - INFO - app - app.py:85 - Session state initialized
2025-07-08 16:29:58,211 - INFO - app - app.py:93 - User authenticated successfully
2025-07-08 16:29:58,212 - INFO - app - app.py:105 - Loading client data from database
2025-07-08 16:29:58,571 - INFO - app - app.py:114 - Successfully loaded data for 1 clients
2025-07-08 16:29:58,571 - INFO - app - app.py:62 - Successfully loaded image: logo/logo_integrum.jpg
2025-07-08 16:29:58,574 - INFO - app - app.py:145 - Client selection: None, Plant: None, Type: None
2025-07-08 16:29:58,575 - INFO - app - app.py:156 - Date range selected: 2025-07-08 to 2025-07-08
2025-07-08 16:29:58,579 - INFO - app - app.py:325 - No client selected, showing welcome message
2025-07-08 16:30:00,727 - INFO - app - app.py:79 - Starting application initialization
2025-07-08 16:30:00,727 - INFO - app - app.py:81 - Page configuration completed
2025-07-08 16:30:00,727 - INFO - app - app.py:85 - Session state initialized
2025-07-08 16:30:00,727 - INFO - app - app.py:93 - User authenticated successfully
2025-07-08 16:30:00,727 - INFO - app - app.py:105 - Loading client data from database
2025-07-08 16:30:00,821 - INFO - app - app.py:114 - Successfully loaded data for 1 clients
2025-07-08 16:30:00,821 - INFO - app - app.py:62 - Successfully loaded image: logo/logo_integrum.jpg
2025-07-08 16:30:00,824 - INFO - app - app.py:145 - Client selection: Kids Clinic India Limited, Plant: None, Type: Combined
2025-07-08 16:30:00,826 - INFO - app - app.py:156 - Date range selected: 2025-07-08 to 2025-07-08
2025-07-08 16:30:00,832 - INFO - app - app.py:184 - Created main application tabs
2025-07-08 16:30:00,832 - INFO - app - app.py:194 - Summary tab: Displaying data for Kids Clinic India Limited
2025-07-08 16:30:00,942 - INFO - app - app.py:218 - Successfully displayed generation vs consumption chart for Kids Clinic India Limited
2025-07-08 16:30:00,942 - INFO - app - app.py:275 - ToD Analysis tab: Displaying data for Kids Clinic India Limited
2025-07-08 16:30:04,330 - INFO - app - app.py:79 - Starting application initialization
2025-07-08 16:30:04,330 - INFO - app - app.py:81 - Page configuration completed
2025-07-08 16:30:04,330 - INFO - app - app.py:85 - Session state initialized
2025-07-08 16:30:04,330 - INFO - app - app.py:93 - User authenticated successfully
2025-07-08 16:30:04,330 - INFO - app - app.py:105 - Loading client data from database
2025-07-08 16:30:04,408 - INFO - app - app.py:114 - Successfully loaded data for 1 clients
2025-07-08 16:30:04,408 - INFO - app - app.py:62 - Successfully loaded image: logo/logo_integrum.jpg
2025-07-08 16:30:04,408 - INFO - app - app.py:145 - Client selection: Kids Clinic India Limited, Plant: None, Type: Combined
2025-07-08 16:30:04,408 - INFO - app - app.py:156 - Date range selected: 2025-05-01 to 2025-05-01
2025-07-08 16:30:04,424 - INFO - app - app.py:184 - Created main application tabs
2025-07-08 16:30:04,424 - INFO - app - app.py:194 - Summary tab: Displaying data for Kids Clinic India Limited
2025-07-08 16:30:04,958 - INFO - app - app.py:218 - Successfully displayed generation vs consumption chart for Kids Clinic India Limited
2025-07-08 16:30:04,958 - INFO - app - app.py:275 - ToD Analysis tab: Displaying data for Kids Clinic India Limited
2025-07-08 16:30:05,467 - INFO - app - app.py:79 - Starting application initialization
2025-07-08 16:30:05,470 - INFO - app - app.py:81 - Page configuration completed
2025-07-08 16:30:05,470 - INFO - app - app.py:85 - Session state initialized
2025-07-08 16:30:05,471 - INFO - app - app.py:93 - User authenticated successfully
2025-07-08 16:30:05,472 - INFO - app - app.py:105 - Loading client data from database
2025-07-08 16:30:05,609 - INFO - app - app.py:114 - Successfully loaded data for 1 clients
2025-07-08 16:30:05,610 - INFO - app - app.py:62 - Successfully loaded image: logo/logo_integrum.jpg
2025-07-08 16:30:05,614 - INFO - app - app.py:145 - Client selection: Kids Clinic India Limited, Plant: None, Type: Combined
2025-07-08 16:30:05,615 - INFO - app - app.py:156 - Date range selected: 2025-05-01 to 2025-05-31
2025-07-08 16:30:05,618 - INFO - app - app.py:184 - Created main application tabs
2025-07-08 16:30:05,618 - INFO - app - app.py:194 - Summary tab: Displaying data for Kids Clinic India Limited
2025-07-08 16:30:06,224 - INFO - app - app.py:218 - Successfully displayed generation vs consumption chart for Kids Clinic India Limited
2025-07-08 16:30:06,226 - INFO - app - app.py:275 - ToD Analysis tab: Displaying data for Kids Clinic India Limited
2025-07-08 16:31:47,198 - INFO - app - app.py:80 - Starting application initialization
2025-07-08 16:31:47,212 - INFO - app - app.py:82 - Page configuration completed
2025-07-08 16:31:47,212 - INFO - app - app.py:86 - Session state initialized
2025-07-08 16:31:47,212 - INFO - app - app.py:94 - User authenticated successfully
2025-07-08 16:31:47,212 - INFO - app - app.py:106 - Loading client data from database
2025-07-08 16:31:47,318 - INFO - app - app.py:115 - Successfully loaded data for 1 clients
2025-07-08 16:31:47,318 - INFO - app - app.py:63 - Successfully loaded image: logo/logo_integrum.jpg
2025-07-08 16:31:47,318 - INFO - app - app.py:146 - Client selection: Kids Clinic India Limited, Plant: None, Type: Combined
2025-07-08 16:31:47,324 - INFO - app - app.py:157 - Date range selected: 2025-05-01 to 2025-05-31
2025-07-08 16:31:47,327 - INFO - app - app.py:185 - Created main application tabs
2025-07-08 16:31:47,328 - INFO - app - app.py:195 - Summary tab: Displaying data for Kids Clinic India Limited
2025-07-08 16:31:47,817 - INFO - app - app.py:210 - Successfully displayed monthly FY analysis for Kids Clinic India Limited
2025-07-08 16:31:47,818 - INFO - app - app.py:267 - ToD Analysis tab: Displaying data for Kids Clinic India Limited
2025-07-08 16:32:00,116 - INFO - app - app.py:80 - Starting application initialization
2025-07-08 16:32:00,167 - INFO - app - app.py:82 - Page configuration completed
2025-07-08 16:32:00,167 - INFO - app - app.py:86 - Session state initialized
2025-07-08 16:32:00,167 - INFO - app - app.py:90 - User not authenticated, showing login form
2025-07-08 16:32:17,802 - INFO - app - app.py:79 - Starting application initialization
2025-07-08 16:32:17,802 - INFO - app - app.py:79 - Starting application initialization
2025-07-08 16:32:17,804 - INFO - app - app.py:81 - Page configuration completed
2025-07-08 16:32:17,914 - INFO - app - app.py:85 - Session state initialized
2025-07-08 16:32:17,923 - INFO - app - app.py:93 - User authenticated successfully
2025-07-08 16:32:17,927 - INFO - app - app.py:105 - Loading client data from database
2025-07-08 16:32:18,128 - INFO - app - app.py:114 - Successfully loaded data for 1 clients
2025-07-08 16:32:18,128 - INFO - app - app.py:62 - Successfully loaded image: logo/logo_integrum.jpg
2025-07-08 16:32:18,130 - INFO - app - app.py:145 - Client selection: Kids Clinic India Limited, Plant: None, Type: Combined
2025-07-08 16:32:18,134 - INFO - app - app.py:156 - Date range selected: 2025-05-01 to 2025-05-31
2025-07-08 16:32:18,139 - INFO - app - app.py:184 - Created main application tabs
2025-07-08 16:32:18,139 - INFO - app - app.py:194 - Summary tab: Displaying data for Kids Clinic India Limited
2025-07-08 16:32:18,804 - INFO - app - app.py:218 - Successfully displayed generation vs consumption chart for Kids Clinic India Limited
2025-07-08 16:32:18,805 - INFO - app - app.py:275 - ToD Analysis tab: Displaying data for Kids Clinic India Limited
2025-07-08 16:36:21,072 - INFO - app - app.py:79 - Starting application initialization
2025-07-08 16:36:21,073 - INFO - app - app.py:81 - Page configuration completed
2025-07-08 16:36:21,073 - INFO - app - app.py:85 - Session state initialized
2025-07-08 16:36:21,073 - INFO - app - app.py:93 - User authenticated successfully
2025-07-08 16:36:21,075 - INFO - app - app.py:105 - Loading client data from database
2025-07-08 16:36:21,159 - INFO - app - app.py:114 - Successfully loaded data for 1 clients
2025-07-08 16:36:21,159 - INFO - app - app.py:62 - Successfully loaded image: logo/logo_integrum.jpg
2025-07-08 16:36:21,159 - INFO - app - app.py:145 - Client selection: Kids Clinic India Limited, Plant: None, Type: Combined
2025-07-08 16:36:21,159 - INFO - app - app.py:156 - Date range selected: 2025-05-01 to 2025-05-31
2025-07-08 16:36:21,169 - INFO - app - app.py:184 - Created main application tabs
2025-07-08 16:36:21,169 - INFO - app - app.py:194 - Summary tab: Displaying data for Kids Clinic India Limited
2025-07-08 16:36:21,602 - INFO - app - app.py:218 - Successfully displayed generation vs consumption chart for Kids Clinic India Limited
2025-07-08 16:36:21,602 - INFO - app - app.py:275 - ToD Analysis tab: Displaying data for Kids Clinic India Limited
2025-07-08 16:36:28,742 - INFO - app - app.py:79 - Starting application initialization
2025-07-08 16:36:28,750 - INFO - app - app.py:81 - Page configuration completed
2025-07-08 16:36:28,750 - INFO - app - app.py:85 - Session state initialized
2025-07-08 16:36:28,750 - INFO - app - app.py:93 - User authenticated successfully
2025-07-08 16:36:28,750 - INFO - app - app.py:105 - Loading client data from database
2025-07-08 16:36:28,900 - INFO - app - app.py:114 - Successfully loaded data for 1 clients
2025-07-08 16:36:28,901 - INFO - app - app.py:62 - Successfully loaded image: logo/logo_integrum.jpg
2025-07-08 16:36:28,902 - INFO - app - app.py:145 - Client selection: Kids Clinic India Limited, Plant: None, Type: Combined
2025-07-08 16:36:28,902 - INFO - app - app.py:156 - Date range selected: 2025-05-01 to 2025-05-31
2025-07-08 16:36:28,902 - INFO - app - app.py:184 - Created main application tabs
2025-07-08 16:36:28,902 - INFO - app - app.py:194 - Summary tab: Displaying data for Kids Clinic India Limited
2025-07-08 16:36:29,385 - INFO - app - app.py:218 - Successfully displayed generation vs consumption chart for Kids Clinic India Limited
2025-07-08 16:36:29,385 - INFO - app - app.py:275 - ToD Analysis tab: Displaying data for Kids Clinic India Limited
2025-07-08 16:37:05,539 - INFO - app - app.py:79 - Starting application initialization
2025-07-08 16:37:05,539 - INFO - app - app.py:81 - Page configuration completed
2025-07-08 16:37:05,539 - INFO - app - app.py:85 - Session state initialized
2025-07-08 16:37:05,539 - INFO - app - app.py:93 - User authenticated successfully
2025-07-08 16:37:05,539 - INFO - app - app.py:105 - Loading client data from database
2025-07-08 16:37:05,648 - INFO - app - app.py:114 - Successfully loaded data for 1 clients
2025-07-08 16:37:05,648 - INFO - app - app.py:62 - Successfully loaded image: logo/logo_integrum.jpg
2025-07-08 16:37:05,650 - INFO - app - app.py:145 - Client selection: Kids Clinic India Limited, Plant: None, Type: Combined
2025-07-08 16:37:05,652 - INFO - app - app.py:156 - Date range selected: 2025-05-01 to 2025-05-31
2025-07-08 16:37:05,655 - INFO - app - app.py:184 - Created main application tabs
2025-07-08 16:37:05,657 - INFO - app - app.py:194 - Summary tab: Displaying data for Kids Clinic India Limited
2025-07-08 16:37:06,250 - INFO - app - app.py:218 - Successfully displayed generation vs consumption chart for Kids Clinic India Limited
2025-07-08 16:37:06,250 - INFO - app - app.py:275 - ToD Analysis tab: Displaying data for Kids Clinic India Limited
2025-07-08 16:37:14,649 - INFO - app - app.py:79 - Starting application initialization
2025-07-08 16:37:14,675 - INFO - app - app.py:81 - Page configuration completed
2025-07-08 16:37:14,675 - INFO - app - app.py:85 - Session state initialized
2025-07-08 16:37:14,675 - INFO - app - app.py:93 - User authenticated successfully
2025-07-08 16:37:14,675 - INFO - app - app.py:105 - Loading client data from database
2025-07-08 16:37:14,792 - INFO - app - app.py:114 - Successfully loaded data for 1 clients
2025-07-08 16:37:14,792 - INFO - app - app.py:62 - Successfully loaded image: logo/logo_integrum.jpg
2025-07-08 16:37:14,794 - INFO - app - app.py:145 - Client selection: Kids Clinic India Limited, Plant: None, Type: Combined
2025-07-08 16:37:14,795 - INFO - app - app.py:156 - Date range selected: 2025-05-01 to 2025-05-31
2025-07-08 16:37:14,798 - INFO - app - app.py:184 - Created main application tabs
2025-07-08 16:37:14,799 - INFO - app - app.py:194 - Summary tab: Displaying data for Kids Clinic India Limited
2025-07-08 16:37:15,326 - INFO - app - app.py:218 - Successfully displayed generation vs consumption chart for Kids Clinic India Limited
2025-07-08 16:37:15,328 - INFO - app - app.py:275 - ToD Analysis tab: Displaying data for Kids Clinic India Limited
2025-07-08 16:37:25,027 - INFO - app - app.py:79 - Starting application initialization
2025-07-08 16:37:25,028 - INFO - app - app.py:81 - Page configuration completed
2025-07-08 16:37:25,029 - INFO - app - app.py:85 - Session state initialized
2025-07-08 16:37:25,029 - INFO - app - app.py:93 - User authenticated successfully
2025-07-08 16:37:25,030 - INFO - app - app.py:105 - Loading client data from database
2025-07-08 16:37:25,149 - INFO - app - app.py:114 - Successfully loaded data for 1 clients
2025-07-08 16:37:25,149 - INFO - app - app.py:62 - Successfully loaded image: logo/logo_integrum.jpg
2025-07-08 16:37:25,150 - INFO - app - app.py:145 - Client selection: Kids Clinic India Limited, Plant: None, Type: Combined
2025-07-08 16:37:25,151 - INFO - app - app.py:156 - Date range selected: 2025-05-01 to 2025-05-31
2025-07-08 16:37:25,155 - INFO - app - app.py:184 - Created main application tabs
2025-07-08 16:37:25,155 - INFO - app - app.py:194 - Summary tab: Displaying data for Kids Clinic India Limited
2025-07-08 16:37:25,640 - INFO - app - app.py:218 - Successfully displayed generation vs consumption chart for Kids Clinic India Limited
2025-07-08 16:37:25,640 - INFO - app - app.py:275 - ToD Analysis tab: Displaying data for Kids Clinic India Limited
2025-07-08 16:37:48,178 - INFO - app - app.py:79 - Starting application initialization
2025-07-08 16:37:48,179 - INFO - app - app.py:81 - Page configuration completed
2025-07-08 16:37:48,180 - INFO - app - app.py:85 - Session state initialized
2025-07-08 16:37:48,180 - INFO - app - app.py:93 - User authenticated successfully
2025-07-08 16:37:48,181 - INFO - app - app.py:105 - Loading client data from database
2025-07-08 16:37:48,275 - INFO - app - app.py:114 - Successfully loaded data for 1 clients
2025-07-08 16:37:48,276 - INFO - app - app.py:62 - Successfully loaded image: logo/logo_integrum.jpg
2025-07-08 16:37:48,278 - INFO - app - app.py:145 - Client selection: Kids Clinic India Limited, Plant: None, Type: Combined
2025-07-08 16:37:48,280 - INFO - app - app.py:156 - Date range selected: 2025-05-01 to 2025-05-31
2025-07-08 16:37:48,283 - INFO - app - app.py:184 - Created main application tabs
2025-07-08 16:37:48,283 - INFO - app - app.py:194 - Summary tab: Displaying data for Kids Clinic India Limited
2025-07-08 16:37:48,775 - INFO - app - app.py:218 - Successfully displayed generation vs consumption chart for Kids Clinic India Limited
2025-07-08 16:37:48,775 - INFO - app - app.py:275 - ToD Analysis tab: Displaying data for Kids Clinic India Limited
2025-07-08 16:37:56,871 - INFO - app - app.py:80 - Starting application initialization
2025-07-08 16:37:56,871 - INFO - app - app.py:82 - Page configuration completed
2025-07-08 16:37:56,873 - INFO - app - app.py:86 - Session state initialized
2025-07-08 16:37:56,874 - INFO - app - app.py:94 - User authenticated successfully
2025-07-08 16:37:56,875 - INFO - app - app.py:106 - Loading client data from database
2025-07-08 16:37:57,050 - INFO - app - app.py:115 - Successfully loaded data for 1 clients
2025-07-08 16:37:57,051 - INFO - app - app.py:63 - Successfully loaded image: logo/logo_integrum.jpg
2025-07-08 16:37:57,053 - INFO - app - app.py:146 - Client selection: Kids Clinic India Limited, Plant: None, Type: Combined
2025-07-08 16:37:57,055 - INFO - app - app.py:157 - Date range selected: 2025-05-01 to 2025-05-31
2025-07-08 16:37:57,063 - INFO - app - app.py:185 - Created main application tabs
2025-07-08 16:37:57,066 - INFO - app - app.py:195 - Summary tab: Displaying data for Kids Clinic India Limited
2025-07-08 16:37:57,693 - INFO - app - app.py:219 - Successfully displayed generation vs consumption chart for Kids Clinic India Limited
2025-07-08 16:37:57,693 - INFO - app - app.py:276 - ToD Analysis tab: Displaying data for Kids Clinic India Limited
2025-07-08 16:38:08,417 - INFO - app - app.py:80 - Starting application initialization
2025-07-08 16:38:08,418 - INFO - app - app.py:82 - Page configuration completed
2025-07-08 16:38:08,419 - INFO - app - app.py:86 - Session state initialized
2025-07-08 16:38:08,419 - INFO - app - app.py:94 - User authenticated successfully
2025-07-08 16:38:08,420 - INFO - app - app.py:106 - Loading client data from database
2025-07-08 16:38:08,562 - INFO - app - app.py:115 - Successfully loaded data for 1 clients
2025-07-08 16:38:08,563 - INFO - app - app.py:63 - Successfully loaded image: logo/logo_integrum.jpg
2025-07-08 16:38:08,564 - INFO - app - app.py:146 - Client selection: Kids Clinic India Limited, Plant: None, Type: Combined
2025-07-08 16:38:08,566 - INFO - app - app.py:157 - Date range selected: 2025-05-01 to 2025-05-31
2025-07-08 16:38:08,567 - INFO - app - app.py:185 - Created main application tabs
2025-07-08 16:38:08,568 - INFO - app - app.py:195 - Summary tab: Displaying data for Kids Clinic India Limited
2025-07-08 16:38:09,116 - INFO - app - app.py:219 - Successfully displayed generation vs consumption chart for Kids Clinic India Limited
2025-07-08 16:38:09,116 - INFO - app - app.py:276 - ToD Analysis tab: Displaying data for Kids Clinic India Limited
2025-07-08 16:38:09,827 - INFO - app - app.py:290 - Successfully displayed last 12 months generation vs consumption for Kids Clinic India Limited
2025-07-08 16:38:20,647 - INFO - app - app.py:80 - Starting application initialization
2025-07-08 16:38:40,002 - INFO - app - app.py:80 - Starting application initialization
2025-07-08 16:38:40,004 - INFO - app - app.py:82 - Page configuration completed
2025-07-08 16:38:40,005 - INFO - app - app.py:86 - Session state initialized
2025-07-08 16:38:40,005 - INFO - app - app.py:90 - User not authenticated, showing login form
2025-07-08 16:38:43,132 - INFO - app - app.py:80 - Starting application initialization
2025-07-08 16:38:43,132 - INFO - app - app.py:82 - Page configuration completed
2025-07-08 16:38:43,134 - INFO - app - app.py:86 - Session state initialized
2025-07-08 16:38:43,134 - INFO - app - app.py:90 - User not authenticated, showing login form
2025-07-08 16:38:44,746 - INFO - app - app.py:80 - Starting application initialization
2025-07-08 16:38:44,746 - INFO - app - app.py:82 - Page configuration completed
2025-07-08 16:38:44,746 - INFO - app - app.py:86 - Session state initialized
2025-07-08 16:38:44,746 - INFO - app - app.py:94 - User authenticated successfully
2025-07-08 16:38:44,746 - INFO - app - app.py:106 - Loading client data from database
2025-07-08 16:38:45,128 - INFO - app - app.py:115 - Successfully loaded data for 1 clients
2025-07-08 16:38:45,128 - INFO - app - app.py:63 - Successfully loaded image: logo/logo_integrum.jpg
2025-07-08 16:38:45,132 - INFO - app - app.py:146 - Client selection: None, Plant: None, Type: None
2025-07-08 16:38:45,132 - INFO - app - app.py:157 - Date range selected: 2025-07-08 to 2025-07-08
2025-07-08 16:38:45,140 - INFO - app - app.py:334 - No client selected, showing welcome message
2025-07-08 16:38:48,163 - INFO - app - app.py:80 - Starting application initialization
2025-07-08 16:38:48,184 - INFO - app - app.py:82 - Page configuration completed
2025-07-08 16:38:48,215 - INFO - app - app.py:86 - Session state initialized
2025-07-08 16:38:48,243 - INFO - app - app.py:94 - User authenticated successfully
2025-07-08 16:38:48,265 - INFO - app - app.py:106 - Loading client data from database
2025-07-08 16:38:48,522 - INFO - app - app.py:115 - Successfully loaded data for 1 clients
2025-07-08 16:38:48,534 - INFO - app - app.py:63 - Successfully loaded image: logo/logo_integrum.jpg
2025-07-08 16:38:48,550 - INFO - app - app.py:146 - Client selection: Kids Clinic India Limited, Plant: None, Type: Combined
2025-07-08 16:38:48,569 - INFO - app - app.py:157 - Date range selected: 2025-07-08 to 2025-07-08
2025-07-08 16:38:48,598 - INFO - app - app.py:185 - Created main application tabs
2025-07-08 16:38:48,610 - INFO - app - app.py:195 - Summary tab: Displaying data for Kids Clinic India Limited
2025-07-08 16:38:48,904 - INFO - app - app.py:219 - Successfully displayed generation vs consumption chart for Kids Clinic India Limited
2025-07-08 16:38:48,919 - INFO - app - app.py:276 - ToD Analysis tab: Displaying data for Kids Clinic India Limited
2025-07-08 16:38:50,272 - INFO - app - app.py:290 - Successfully displayed last 12 months generation vs consumption for Kids Clinic India Limited
2025-07-08 16:39:48,888 - INFO - app - app.py:80 - Starting application initialization
2025-07-08 16:39:48,889 - INFO - app - app.py:82 - Page configuration completed
2025-07-08 16:39:48,890 - INFO - app - app.py:86 - Session state initialized
2025-07-08 16:39:48,891 - INFO - app - app.py:94 - User authenticated successfully
2025-07-08 16:39:48,891 - INFO - app - app.py:106 - Loading client data from database
2025-07-08 16:39:49,004 - INFO - app - app.py:115 - Successfully loaded data for 1 clients
2025-07-08 16:39:49,004 - INFO - app - app.py:63 - Successfully loaded image: logo/logo_integrum.jpg
2025-07-08 16:39:49,004 - INFO - app - app.py:146 - Client selection: Kids Clinic India Limited, Plant: None, Type: Combined
2025-07-08 16:39:49,004 - INFO - app - app.py:157 - Date range selected: 2025-05-01 to 2025-05-31
2025-07-08 16:39:49,020 - INFO - app - app.py:185 - Created main application tabs
2025-07-08 16:39:49,020 - INFO - app - app.py:195 - Summary tab: Displaying data for Kids Clinic India Limited
2025-07-08 16:39:49,124 - INFO - app - app.py:220 - Successfully displayed generation vs consumption chart for Kids Clinic India Limited
2025-07-08 16:39:49,124 - INFO - app - app.py:277 - ToD Analysis tab: Displaying data for Kids Clinic India Limited
2025-07-08 16:39:49,633 - INFO - app - app.py:291 - Successfully displayed last 12 months generation vs consumption for Kids Clinic India Limited
2025-07-08 16:40:00,770 - INFO - app - app.py:80 - Starting application initialization
2025-07-08 16:40:00,770 - INFO - app - app.py:82 - Page configuration completed
2025-07-08 16:40:00,770 - INFO - app - app.py:86 - Session state initialized
2025-07-08 16:40:00,770 - INFO - app - app.py:94 - User authenticated successfully
2025-07-08 16:40:00,770 - INFO - app - app.py:106 - Loading client data from database
2025-07-08 16:40:00,890 - INFO - app - app.py:115 - Successfully loaded data for 1 clients
2025-07-08 16:40:00,890 - INFO - app - app.py:63 - Successfully loaded image: logo/logo_integrum.jpg
2025-07-08 16:40:00,892 - INFO - app - app.py:146 - Client selection: Kids Clinic India Limited, Plant: None, Type: Combined
2025-07-08 16:40:00,892 - INFO - app - app.py:157 - Date range selected: 2025-05-01 to 2025-05-31
2025-07-08 16:40:00,896 - INFO - app - app.py:185 - Created main application tabs
2025-07-08 16:40:00,897 - INFO - app - app.py:195 - Summary tab: Displaying data for Kids Clinic India Limited
2025-07-08 16:40:01,374 - INFO - app - app.py:220 - Successfully displayed generation vs consumption chart for Kids Clinic India Limited
2025-07-08 16:40:01,375 - INFO - app - app.py:277 - ToD Analysis tab: Displaying data for Kids Clinic India Limited
2025-07-08 16:40:01,816 - INFO - app - app.py:291 - Successfully displayed last 12 months generation vs consumption for Kids Clinic India Limited
2025-07-08 16:41:10,626 - INFO - app - app.py:79 - Starting application initialization
2025-07-08 16:41:10,627 - INFO - app - app.py:81 - Page configuration completed
2025-07-08 16:41:10,628 - INFO - app - app.py:85 - Session state initialized
2025-07-08 16:41:10,629 - INFO - app - app.py:93 - User authenticated successfully
2025-07-08 16:41:10,629 - INFO - app - app.py:105 - Loading client data from database
2025-07-08 16:41:10,708 - INFO - app - app.py:114 - Successfully loaded data for 1 clients
2025-07-08 16:41:10,708 - INFO - app - app.py:62 - Successfully loaded image: logo/logo_integrum.jpg
2025-07-08 16:41:10,713 - INFO - app - app.py:145 - Client selection: Kids Clinic India Limited, Plant: None, Type: Combined
2025-07-08 16:41:10,713 - INFO - app - app.py:156 - Date range selected: 2025-05-01 to 2025-05-31
2025-07-08 16:41:10,717 - INFO - app - app.py:184 - Created main application tabs
2025-07-08 16:41:10,718 - INFO - app - app.py:194 - Summary tab: Displaying data for Kids Clinic India Limited
2025-07-08 16:41:11,290 - INFO - app - app.py:209 - Successfully displayed generation vs consumption chart for Kids Clinic India Limited
2025-07-08 16:41:11,290 - INFO - app - app.py:266 - ToD Analysis tab: Displaying data for Kids Clinic India Limited
2025-07-08 16:42:50,206 - INFO - app - app.py:79 - Starting application initialization
2025-07-08 16:42:50,208 - INFO - app - app.py:81 - Page configuration completed
2025-07-08 16:42:50,208 - INFO - app - app.py:85 - Session state initialized
2025-07-08 16:42:50,208 - INFO - app - app.py:93 - User authenticated successfully
2025-07-08 16:42:50,209 - INFO - app - app.py:105 - Loading client data from database
2025-07-08 16:42:50,321 - INFO - app - app.py:114 - Successfully loaded data for 1 clients
2025-07-08 16:42:50,322 - INFO - app - app.py:62 - Successfully loaded image: logo/logo_integrum.jpg
2025-07-08 16:42:50,325 - INFO - app - app.py:145 - Client selection: Kids Clinic India Limited, Plant: None, Type: Combined
2025-07-08 16:42:50,327 - INFO - app - app.py:156 - Date range selected: 2025-05-01 to 2025-05-31
2025-07-08 16:42:50,330 - INFO - app - app.py:184 - Created main application tabs
2025-07-08 16:42:50,330 - INFO - app - app.py:194 - Summary tab: Displaying data for Kids Clinic India Limited
2025-07-08 16:42:50,931 - INFO - app - app.py:209 - Successfully displayed generation vs consumption chart for Kids Clinic India Limited
2025-07-08 16:42:50,932 - INFO - app - app.py:266 - ToD Analysis tab: Displaying data for Kids Clinic India Limited
2025-07-08 16:43:02,855 - INFO - app - app.py:79 - Starting application initialization
2025-07-08 16:43:02,855 - INFO - app - app.py:81 - Page configuration completed
2025-07-08 16:43:02,855 - INFO - app - app.py:85 - Session state initialized
2025-07-08 16:43:02,855 - INFO - app - app.py:93 - User authenticated successfully
2025-07-08 16:43:02,855 - INFO - app - app.py:105 - Loading client data from database
2025-07-08 16:43:02,967 - INFO - app - app.py:114 - Successfully loaded data for 1 clients
2025-07-08 16:43:03,006 - INFO - app - app.py:62 - Successfully loaded image: logo/logo_integrum.jpg
2025-07-08 16:43:03,010 - INFO - app - app.py:145 - Client selection: Kids Clinic India Limited, Plant: None, Type: Combined
2025-07-08 16:43:03,012 - INFO - app - app.py:156 - Date range selected: 2025-05-01 to 2025-05-31
2025-07-08 16:43:03,021 - INFO - app - app.py:184 - Created main application tabs
2025-07-08 16:43:03,028 - INFO - app - app.py:194 - Summary tab: Displaying data for Kids Clinic India Limited
2025-07-08 16:43:04,167 - INFO - app - app.py:209 - Successfully displayed generation vs consumption chart for Kids Clinic India Limited
2025-07-08 16:43:04,167 - INFO - app - app.py:266 - ToD Analysis tab: Displaying data for Kids Clinic India Limited
2025-07-08 16:45:00,586 - INFO - app - app.py:79 - Starting application initialization
2025-07-08 16:45:00,588 - INFO - app - app.py:81 - Page configuration completed
2025-07-08 16:45:00,588 - INFO - app - app.py:85 - Session state initialized
2025-07-08 16:45:00,588 - INFO - app - app.py:93 - User authenticated successfully
2025-07-08 16:45:00,593 - INFO - app - app.py:105 - Loading client data from database
2025-07-08 16:45:00,715 - INFO - app - app.py:114 - Successfully loaded data for 1 clients
2025-07-08 16:45:00,715 - INFO - app - app.py:62 - Successfully loaded image: logo/logo_integrum.jpg
2025-07-08 16:45:00,715 - INFO - app - app.py:145 - Client selection: Kids Clinic India Limited, Plant: None, Type: Combined
2025-07-08 16:45:00,724 - INFO - app - app.py:156 - Date range selected: 2025-05-01 to 2025-05-31
2025-07-08 16:45:00,728 - INFO - app - app.py:184 - Created main application tabs
2025-07-08 16:45:00,728 - INFO - app - app.py:194 - Summary tab: Displaying data for Kids Clinic India Limited
2025-07-08 16:45:02,047 - INFO - app - app.py:209 - Successfully displayed generation vs consumption chart for Kids Clinic India Limited
2025-07-08 16:45:02,049 - INFO - app - app.py:266 - ToD Analysis tab: Displaying data for Kids Clinic India Limited
2025-07-08 16:45:12,934 - INFO - app - app.py:79 - Starting application initialization
2025-07-08 16:45:12,937 - INFO - app - app.py:81 - Page configuration completed
2025-07-08 16:45:12,939 - INFO - app - app.py:85 - Session state initialized
2025-07-08 16:45:12,939 - INFO - app - app.py:93 - User authenticated successfully
2025-07-08 16:45:12,943 - INFO - app - app.py:105 - Loading client data from database
2025-07-08 16:45:13,089 - INFO - app - app.py:114 - Successfully loaded data for 1 clients
2025-07-08 16:45:13,089 - INFO - app - app.py:62 - Successfully loaded image: logo/logo_integrum.jpg
2025-07-08 16:45:13,094 - INFO - app - app.py:145 - Client selection: Kids Clinic India Limited, Plant: None, Type: Combined
2025-07-08 16:45:13,096 - INFO - app - app.py:156 - Date range selected: 2025-05-01 to 2025-05-31
2025-07-08 16:45:13,102 - INFO - app - app.py:184 - Created main application tabs
2025-07-08 16:45:13,103 - INFO - app - app.py:194 - Summary tab: Displaying data for Kids Clinic India Limited
2025-07-08 16:45:14,440 - INFO - app - app.py:209 - Successfully displayed generation vs consumption chart for Kids Clinic India Limited
2025-07-08 16:45:14,445 - INFO - app - app.py:266 - ToD Analysis tab: Displaying data for Kids Clinic India Limited
2025-07-08 16:45:39,380 - INFO - app - app.py:79 - Starting application initialization
2025-07-08 16:45:39,380 - INFO - app - app.py:81 - Page configuration completed
2025-07-08 16:45:39,380 - INFO - app - app.py:85 - Session state initialized
2025-07-08 16:45:39,380 - INFO - app - app.py:93 - User authenticated successfully
2025-07-08 16:45:39,380 - INFO - app - app.py:105 - Loading client data from database
2025-07-08 16:45:39,507 - INFO - app - app.py:114 - Successfully loaded data for 1 clients
2025-07-08 16:45:39,508 - INFO - app - app.py:62 - Successfully loaded image: logo/logo_integrum.jpg
2025-07-08 16:45:39,511 - INFO - app - app.py:145 - Client selection: Kids Clinic India Limited, Plant: None, Type: Combined
2025-07-08 16:45:39,512 - INFO - app - app.py:156 - Date range selected: 2025-05-01 to 2025-05-31
2025-07-08 16:45:39,515 - INFO - app - app.py:184 - Created main application tabs
2025-07-08 16:45:39,515 - INFO - app - app.py:194 - Summary tab: Displaying data for Kids Clinic India Limited
2025-07-08 16:45:40,514 - INFO - app - app.py:209 - Successfully displayed generation vs consumption chart for Kids Clinic India Limited
2025-07-08 16:45:40,514 - INFO - app - app.py:266 - ToD Analysis tab: Displaying data for Kids Clinic India Limited
2025-07-08 16:46:25,334 - INFO - app - app.py:79 - Starting application initialization
2025-07-08 16:46:25,334 - INFO - app - app.py:81 - Page configuration completed
2025-07-08 16:46:25,334 - INFO - app - app.py:85 - Session state initialized
2025-07-08 16:46:25,334 - INFO - app - app.py:93 - User authenticated successfully
2025-07-08 16:46:25,334 - INFO - app - app.py:105 - Loading client data from database
2025-07-08 16:46:25,462 - INFO - app - app.py:114 - Successfully loaded data for 1 clients
2025-07-08 16:46:25,462 - INFO - app - app.py:62 - Successfully loaded image: logo/logo_integrum.jpg
2025-07-08 16:46:25,465 - INFO - app - app.py:145 - Client selection: Kids Clinic India Limited, Plant: None, Type: Combined
2025-07-08 16:46:25,466 - INFO - app - app.py:156 - Date range selected: 2025-05-01 to 2025-05-31
2025-07-08 16:46:25,470 - INFO - app - app.py:184 - Created main application tabs
2025-07-08 16:46:25,471 - INFO - app - app.py:194 - Summary tab: Displaying data for Kids Clinic India Limited
2025-07-08 16:46:26,599 - INFO - app - app.py:209 - Successfully displayed generation vs consumption chart for Kids Clinic India Limited
2025-07-08 16:46:26,599 - INFO - app - app.py:266 - ToD Analysis tab: Displaying data for Kids Clinic India Limited
2025-07-08 16:47:15,368 - INFO - app - app.py:79 - Starting application initialization
2025-07-08 16:47:15,368 - INFO - app - app.py:81 - Page configuration completed
2025-07-08 16:47:15,368 - INFO - app - app.py:85 - Session state initialized
2025-07-08 16:47:15,368 - INFO - app - app.py:93 - User authenticated successfully
2025-07-08 16:47:15,368 - INFO - app - app.py:105 - Loading client data from database
2025-07-08 16:47:15,554 - INFO - app - app.py:114 - Successfully loaded data for 1 clients
2025-07-08 16:47:15,555 - INFO - app - app.py:62 - Successfully loaded image: logo/logo_integrum.jpg
2025-07-08 16:47:15,560 - INFO - app - app.py:145 - Client selection: Kids Clinic India Limited, Plant: None, Type: Combined
2025-07-08 16:47:15,562 - INFO - app - app.py:156 - Date range selected: 2025-05-01 to 2025-05-31
2025-07-08 16:47:15,571 - INFO - app - app.py:184 - Created main application tabs
2025-07-08 16:47:15,571 - INFO - app - app.py:194 - Summary tab: Displaying data for Kids Clinic India Limited
2025-07-08 16:47:16,561 - INFO - app - app.py:209 - Successfully displayed generation vs consumption chart for Kids Clinic India Limited
2025-07-08 16:47:16,561 - INFO - app - app.py:266 - ToD Analysis tab: Displaying data for Kids Clinic India Limited
2025-07-08 16:47:38,736 - INFO - app - app.py:79 - Starting application initialization
2025-07-08 16:47:38,736 - INFO - app - app.py:81 - Page configuration completed
2025-07-08 16:47:38,736 - INFO - app - app.py:85 - Session state initialized
2025-07-08 16:47:38,753 - INFO - app - app.py:93 - User authenticated successfully
2025-07-08 16:47:38,756 - INFO - app - app.py:105 - Loading client data from database
2025-07-08 16:47:38,855 - INFO - app - app.py:114 - Successfully loaded data for 1 clients
2025-07-08 16:47:38,855 - INFO - app - app.py:62 - Successfully loaded image: logo/logo_integrum.jpg
2025-07-08 16:47:38,855 - INFO - app - app.py:145 - Client selection: None, Plant: None, Type: None
2025-07-08 16:47:38,855 - INFO - app - app.py:156 - Date range selected: 2025-07-08 to 2025-07-08
2025-07-08 16:47:38,870 - INFO - app - app.py:316 - No client selected, showing welcome message
2025-07-08 16:47:39,987 - INFO - app - app.py:79 - Starting application initialization
2025-07-08 16:47:39,988 - INFO - app - app.py:81 - Page configuration completed
2025-07-08 16:47:39,988 - INFO - app - app.py:85 - Session state initialized
2025-07-08 16:47:39,990 - INFO - app - app.py:93 - User authenticated successfully
2025-07-08 16:47:39,990 - INFO - app - app.py:105 - Loading client data from database
2025-07-08 16:47:40,071 - INFO - app - app.py:114 - Successfully loaded data for 1 clients
2025-07-08 16:47:40,071 - INFO - app - app.py:62 - Successfully loaded image: logo/logo_integrum.jpg
2025-07-08 16:47:40,087 - INFO - app - app.py:145 - Client selection: None, Plant: None, Type: None
2025-07-08 16:47:40,088 - INFO - app - app.py:156 - Date range selected: 2025-07-08 to 2025-07-08
2025-07-08 16:47:40,091 - INFO - app - app.py:316 - No client selected, showing welcome message
2025-07-08 16:47:43,475 - INFO - app - app.py:79 - Starting application initialization
2025-07-08 16:47:43,477 - INFO - app - app.py:81 - Page configuration completed
2025-07-08 16:47:43,477 - INFO - app - app.py:85 - Session state initialized
2025-07-08 16:47:43,477 - INFO - app - app.py:93 - User authenticated successfully
2025-07-08 16:47:43,479 - INFO - app - app.py:105 - Loading client data from database
2025-07-08 16:47:43,553 - INFO - app - app.py:114 - Successfully loaded data for 1 clients
2025-07-08 16:47:43,569 - INFO - app - app.py:62 - Successfully loaded image: logo/logo_integrum.jpg
2025-07-08 16:47:43,570 - INFO - app - app.py:145 - Client selection: Kids Clinic India Limited, Plant: None, Type: Combined
2025-07-08 16:47:43,570 - INFO - app - app.py:156 - Date range selected: 2025-07-08 to 2025-07-08
2025-07-08 16:47:43,570 - INFO - app - app.py:184 - Created main application tabs
2025-07-08 16:47:43,570 - INFO - app - app.py:194 - Summary tab: Displaying data for Kids Clinic India Limited
2025-07-08 16:47:45,653 - INFO - app - app.py:209 - Successfully displayed generation vs consumption chart for Kids Clinic India Limited
2025-07-08 16:47:45,653 - INFO - app - app.py:266 - ToD Analysis tab: Displaying data for Kids Clinic India Limited
2025-07-08 16:47:51,523 - INFO - app - app.py:79 - Starting application initialization
2025-07-08 16:47:51,523 - INFO - app - app.py:81 - Page configuration completed
2025-07-08 16:47:51,523 - INFO - app - app.py:85 - Session state initialized
2025-07-08 16:47:51,536 - INFO - app - app.py:93 - User authenticated successfully
2025-07-08 16:47:51,539 - INFO - app - app.py:105 - Loading client data from database
2025-07-08 16:47:51,656 - INFO - app - app.py:114 - Successfully loaded data for 1 clients
2025-07-08 16:47:51,656 - INFO - app - app.py:62 - Successfully loaded image: logo/logo_integrum.jpg
2025-07-08 16:47:51,669 - INFO - app - app.py:145 - Client selection: Kids Clinic India Limited, Plant: None, Type: Combined
2025-07-08 16:47:51,671 - INFO - app - app.py:156 - Date range selected: 2025-07-08 to 2025-07-08
2025-07-08 16:47:51,676 - INFO - app - app.py:184 - Created main application tabs
2025-07-08 16:47:51,677 - INFO - app - app.py:194 - Summary tab: Displaying data for Kids Clinic India Limited
2025-07-08 16:47:52,588 - INFO - app - app.py:209 - Successfully displayed generation vs consumption chart for Kids Clinic India Limited
2025-07-08 16:47:52,588 - INFO - app - app.py:266 - ToD Analysis tab: Displaying data for Kids Clinic India Limited
2025-07-08 16:48:07,028 - INFO - app - app.py:79 - Starting application initialization
2025-07-08 16:48:07,028 - INFO - app - app.py:81 - Page configuration completed
2025-07-08 16:48:07,028 - INFO - app - app.py:85 - Session state initialized
2025-07-08 16:48:07,028 - INFO - app - app.py:93 - User authenticated successfully
2025-07-08 16:48:07,028 - INFO - app - app.py:105 - Loading client data from database
2025-07-08 16:48:07,129 - INFO - app - app.py:114 - Successfully loaded data for 1 clients
2025-07-08 16:48:07,129 - INFO - app - app.py:62 - Successfully loaded image: logo/logo_integrum.jpg
2025-07-08 16:48:07,129 - INFO - app - app.py:145 - Client selection: Kids Clinic India Limited, Plant: None, Type: Combined
2025-07-08 16:48:07,139 - INFO - app - app.py:156 - Date range selected: 2025-07-08 to 2025-07-08
2025-07-08 16:48:07,142 - INFO - app - app.py:184 - Created main application tabs
2025-07-08 16:48:07,143 - INFO - app - app.py:194 - Summary tab: Displaying data for Kids Clinic India Limited
2025-07-08 16:48:08,139 - INFO - app - app.py:209 - Successfully displayed generation vs consumption chart for Kids Clinic India Limited
2025-07-08 16:48:08,139 - INFO - app - app.py:266 - ToD Analysis tab: Displaying data for Kids Clinic India Limited
2025-07-08 16:48:44,094 - INFO - app - app.py:79 - Starting application initialization
2025-07-08 16:48:44,095 - INFO - app - app.py:81 - Page configuration completed
2025-07-08 16:48:44,095 - INFO - app - app.py:85 - Session state initialized
2025-07-08 16:48:44,096 - INFO - app - app.py:93 - User authenticated successfully
2025-07-08 16:48:44,097 - INFO - app - app.py:105 - Loading client data from database
2025-07-08 16:48:44,216 - INFO - app - app.py:114 - Successfully loaded data for 1 clients
2025-07-08 16:48:44,217 - INFO - app - app.py:62 - Successfully loaded image: logo/logo_integrum.jpg
2025-07-08 16:48:44,218 - INFO - app - app.py:145 - Client selection: Kids Clinic India Limited, Plant: None, Type: Combined
2025-07-08 16:48:44,219 - INFO - app - app.py:156 - Date range selected: 2025-07-08 to 2025-07-08
2025-07-08 16:48:44,221 - INFO - app - app.py:184 - Created main application tabs
2025-07-08 16:48:44,222 - INFO - app - app.py:194 - Summary tab: Displaying data for Kids Clinic India Limited
2025-07-08 16:48:45,125 - INFO - app - app.py:209 - Successfully displayed generation vs consumption chart for Kids Clinic India Limited
2025-07-08 16:48:45,141 - INFO - app - app.py:266 - ToD Analysis tab: Displaying data for Kids Clinic India Limited
2025-07-08 16:49:14,031 - INFO - app - app.py:79 - Starting application initialization
2025-07-08 16:49:14,031 - INFO - app - app.py:81 - Page configuration completed
2025-07-08 16:49:14,031 - INFO - app - app.py:85 - Session state initialized
2025-07-08 16:49:14,031 - INFO - app - app.py:93 - User authenticated successfully
2025-07-08 16:49:14,031 - INFO - app - app.py:105 - Loading client data from database
2025-07-08 16:49:14,163 - INFO - app - app.py:114 - Successfully loaded data for 1 clients
2025-07-08 16:49:14,163 - INFO - app - app.py:62 - Successfully loaded image: logo/logo_integrum.jpg
2025-07-08 16:49:14,163 - INFO - app - app.py:145 - Client selection: Kids Clinic India Limited, Plant: None, Type: Combined
2025-07-08 16:49:14,168 - INFO - app - app.py:156 - Date range selected: 2025-07-08 to 2025-07-08
2025-07-08 16:49:14,170 - INFO - app - app.py:184 - Created main application tabs
2025-07-08 16:49:14,170 - INFO - app - app.py:194 - Summary tab: Displaying data for Kids Clinic India Limited
2025-07-08 16:49:15,245 - INFO - app - app.py:209 - Successfully displayed generation vs consumption chart for Kids Clinic India Limited
2025-07-08 16:49:15,250 - INFO - app - app.py:266 - ToD Analysis tab: Displaying data for Kids Clinic India Limited
2025-07-08 16:49:57,983 - INFO - app - app.py:79 - Starting application initialization
2025-07-08 16:49:57,983 - INFO - app - app.py:81 - Page configuration completed
2025-07-08 16:49:57,983 - INFO - app - app.py:85 - Session state initialized
2025-07-08 16:49:57,983 - INFO - app - app.py:93 - User authenticated successfully
2025-07-08 16:49:57,983 - INFO - app - app.py:105 - Loading client data from database
2025-07-08 16:49:58,102 - INFO - app - app.py:114 - Successfully loaded data for 1 clients
2025-07-08 16:49:58,103 - INFO - app - app.py:62 - Successfully loaded image: logo/logo_integrum.jpg
2025-07-08 16:49:58,105 - INFO - app - app.py:145 - Client selection: Kids Clinic India Limited, Plant: None, Type: Combined
2025-07-08 16:49:58,107 - INFO - app - app.py:156 - Date range selected: 2025-07-08 to 2025-07-08
2025-07-08 16:49:58,109 - INFO - app - app.py:184 - Created main application tabs
2025-07-08 16:49:58,110 - INFO - app - app.py:194 - Summary tab: Displaying data for Kids Clinic India Limited
2025-07-08 16:49:59,150 - INFO - app - app.py:209 - Successfully displayed generation vs consumption chart for Kids Clinic India Limited
2025-07-08 16:49:59,152 - INFO - app - app.py:266 - ToD Analysis tab: Displaying data for Kids Clinic India Limited
2025-07-08 16:56:16,198 - INFO - app - app.py:79 - Starting application initialization
2025-07-08 16:56:16,201 - INFO - app - app.py:81 - Page configuration completed
2025-07-08 16:56:16,202 - INFO - app - app.py:85 - Session state initialized
2025-07-08 16:56:16,202 - INFO - app - app.py:93 - User authenticated successfully
2025-07-08 16:56:16,203 - INFO - app - app.py:105 - Loading client data from database
2025-07-08 16:56:16,383 - INFO - app - app.py:114 - Successfully loaded data for 1 clients
2025-07-08 16:56:16,383 - INFO - app - app.py:62 - Successfully loaded image: logo/logo_integrum.jpg
2025-07-08 16:56:16,386 - INFO - app - app.py:145 - Client selection: Kids Clinic India Limited, Plant: None, Type: Combined
2025-07-08 16:56:16,394 - INFO - app - app.py:156 - Date range selected: 2025-07-08 to 2025-07-08
2025-07-08 16:56:16,398 - INFO - app - app.py:184 - Created main application tabs
2025-07-08 16:56:16,399 - INFO - app - app.py:194 - Summary tab: Displaying data for Kids Clinic India Limited
2025-07-08 16:56:17,418 - INFO - app - app.py:209 - Successfully displayed generation vs consumption chart for Kids Clinic India Limited
2025-07-08 16:56:17,419 - INFO - app - app.py:266 - ToD Analysis tab: Displaying data for Kids Clinic India Limited
2025-07-08 16:56:33,833 - INFO - app - app.py:79 - Starting application initialization
2025-07-08 16:56:33,835 - INFO - app - app.py:81 - Page configuration completed
2025-07-08 16:56:33,835 - INFO - app - app.py:85 - Session state initialized
2025-07-08 16:56:33,837 - INFO - app - app.py:93 - User authenticated successfully
2025-07-08 16:56:33,837 - INFO - app - app.py:105 - Loading client data from database
2025-07-08 16:56:33,958 - INFO - app - app.py:114 - Successfully loaded data for 1 clients
2025-07-08 16:56:33,958 - INFO - app - app.py:62 - Successfully loaded image: logo/logo_integrum.jpg
2025-07-08 16:56:33,974 - INFO - app - app.py:145 - Client selection: Kids Clinic India Limited, Plant: None, Type: Combined
2025-07-08 16:56:33,974 - INFO - app - app.py:156 - Date range selected: 2025-07-08 to 2025-07-08
2025-07-08 16:56:33,974 - INFO - app - app.py:184 - Created main application tabs
2025-07-08 16:56:33,974 - INFO - app - app.py:194 - Summary tab: Displaying data for Kids Clinic India Limited
2025-07-08 16:56:35,258 - INFO - app - app.py:209 - Successfully displayed generation vs consumption chart for Kids Clinic India Limited
2025-07-08 16:56:35,258 - INFO - app - app.py:266 - ToD Analysis tab: Displaying data for Kids Clinic India Limited
2025-07-08 16:57:11,432 - INFO - app - app.py:79 - Starting application initialization
2025-07-08 16:57:11,432 - INFO - app - app.py:81 - Page configuration completed
2025-07-08 16:57:11,432 - INFO - app - app.py:85 - Session state initialized
2025-07-08 16:57:11,432 - INFO - app - app.py:93 - User authenticated successfully
2025-07-08 16:57:11,432 - INFO - app - app.py:105 - Loading client data from database
2025-07-08 16:57:11,572 - INFO - app - app.py:114 - Successfully loaded data for 1 clients
2025-07-08 16:57:11,573 - INFO - app - app.py:62 - Successfully loaded image: logo/logo_integrum.jpg
2025-07-08 16:57:11,575 - INFO - app - app.py:145 - Client selection: Kids Clinic India Limited, Plant: None, Type: Combined
2025-07-08 16:57:11,577 - INFO - app - app.py:156 - Date range selected: 2025-07-08 to 2025-07-08
2025-07-08 16:57:11,580 - INFO - app - app.py:184 - Created main application tabs
2025-07-08 16:57:11,580 - INFO - app - app.py:194 - Summary tab: Displaying data for Kids Clinic India Limited
2025-07-08 16:57:12,434 - INFO - app - app.py:209 - Successfully displayed generation vs consumption chart for Kids Clinic India Limited
2025-07-08 16:57:12,445 - INFO - app - app.py:266 - ToD Analysis tab: Displaying data for Kids Clinic India Limited
2025-07-08 16:57:40,900 - INFO - app - app.py:79 - Starting application initialization
2025-07-08 16:57:40,900 - INFO - app - app.py:81 - Page configuration completed
2025-07-08 16:57:40,902 - INFO - app - app.py:85 - Session state initialized
2025-07-08 16:57:40,902 - INFO - app - app.py:93 - User authenticated successfully
2025-07-08 16:57:40,902 - INFO - app - app.py:105 - Loading client data from database
2025-07-08 16:57:41,015 - INFO - app - app.py:114 - Successfully loaded data for 1 clients
2025-07-08 16:57:41,015 - INFO - app - app.py:62 - Successfully loaded image: logo/logo_integrum.jpg
2025-07-08 16:57:41,020 - INFO - app - app.py:145 - Client selection: Kids Clinic India Limited, Plant: None, Type: Combined
2025-07-08 16:57:41,020 - INFO - app - app.py:156 - Date range selected: 2025-07-08 to 2025-07-08
2025-07-08 16:57:41,026 - INFO - app - app.py:184 - Created main application tabs
2025-07-08 16:57:41,026 - INFO - app - app.py:194 - Summary tab: Displaying data for Kids Clinic India Limited
2025-07-08 16:57:41,112 - INFO - app - app.py:79 - Starting application initialization
2025-07-08 16:57:41,120 - INFO - app - app.py:81 - Page configuration completed
2025-07-08 16:57:41,120 - INFO - app - app.py:85 - Session state initialized
2025-07-08 16:57:41,120 - INFO - app - app.py:93 - User authenticated successfully
2025-07-08 16:57:41,122 - INFO - app - app.py:105 - Loading client data from database
2025-07-08 16:57:41,290 - INFO - app - app.py:114 - Successfully loaded data for 1 clients
2025-07-08 16:57:41,290 - INFO - app - app.py:62 - Successfully loaded image: logo/logo_integrum.jpg
2025-07-08 16:57:41,294 - INFO - app - app.py:145 - Client selection: Kids Clinic India Limited, Plant: None, Type: Combined
2025-07-08 16:57:41,303 - INFO - app - app.py:156 - Date range selected: 2025-07-08 to 2025-07-08
2025-07-08 16:57:41,308 - INFO - app - app.py:184 - Created main application tabs
2025-07-08 16:57:41,313 - INFO - app - app.py:194 - Summary tab: Displaying data for Kids Clinic India Limited
2025-07-08 16:57:43,670 - INFO - app - app.py:209 - Successfully displayed generation vs consumption chart for Kids Clinic India Limited
2025-07-08 16:57:43,670 - INFO - app - app.py:266 - ToD Analysis tab: Displaying data for Kids Clinic India Limited
2025-07-08 16:57:53,349 - INFO - app - app.py:79 - Starting application initialization
2025-07-08 16:57:53,349 - INFO - app - app.py:81 - Page configuration completed
2025-07-08 16:57:53,349 - INFO - app - app.py:85 - Session state initialized
2025-07-08 16:57:53,349 - INFO - app - app.py:93 - User authenticated successfully
2025-07-08 16:57:53,365 - INFO - app - app.py:105 - Loading client data from database
2025-07-08 16:57:53,434 - INFO - app - app.py:114 - Successfully loaded data for 1 clients
2025-07-08 16:57:53,434 - INFO - app - app.py:62 - Successfully loaded image: logo/logo_integrum.jpg
2025-07-08 16:57:53,449 - INFO - app - app.py:145 - Client selection: Kids Clinic India Limited, Plant: None, Type: Combined
2025-07-08 16:57:53,450 - INFO - app - app.py:156 - Date range selected: 2025-07-08 to 2025-07-08
2025-07-08 16:57:53,454 - INFO - app - app.py:184 - Created main application tabs
2025-07-08 16:57:53,454 - INFO - app - app.py:194 - Summary tab: Displaying data for Kids Clinic India Limited
2025-07-08 16:57:55,538 - INFO - app - app.py:209 - Successfully displayed generation vs consumption chart for Kids Clinic India Limited
2025-07-08 16:57:55,548 - INFO - app - app.py:266 - ToD Analysis tab: Displaying data for Kids Clinic India Limited
2025-07-08 16:58:00,170 - INFO - app - app.py:79 - Starting application initialization
2025-07-08 16:58:00,172 - INFO - app - app.py:81 - Page configuration completed
2025-07-08 16:58:00,174 - INFO - app - app.py:85 - Session state initialized
2025-07-08 16:58:00,174 - INFO - app - app.py:93 - User authenticated successfully
2025-07-08 16:58:00,176 - INFO - app - app.py:105 - Loading client data from database
2025-07-08 16:58:00,282 - INFO - app - app.py:114 - Successfully loaded data for 1 clients
2025-07-08 16:58:00,282 - INFO - app - app.py:62 - Successfully loaded image: logo/logo_integrum.jpg
2025-07-08 16:58:00,282 - INFO - app - app.py:145 - Client selection: Kids Clinic India Limited, Plant: None, Type: Combined
2025-07-08 16:58:00,300 - INFO - app - app.py:156 - Date range selected: 2025-07-08 to 2025-07-08
2025-07-08 16:58:00,300 - INFO - app - app.py:184 - Created main application tabs
2025-07-08 16:58:00,300 - INFO - app - app.py:194 - Summary tab: Displaying data for Kids Clinic India Limited
2025-07-08 16:58:01,380 - INFO - app - app.py:209 - Successfully displayed generation vs consumption chart for Kids Clinic India Limited
2025-07-08 16:58:01,380 - INFO - app - app.py:266 - ToD Analysis tab: Displaying data for Kids Clinic India Limited
2025-07-08 16:58:30,635 - INFO - app - app.py:79 - Starting application initialization
2025-07-08 16:58:30,638 - INFO - app - app.py:81 - Page configuration completed
2025-07-08 16:58:30,640 - INFO - app - app.py:85 - Session state initialized
2025-07-08 16:58:30,640 - INFO - app - app.py:93 - User authenticated successfully
2025-07-08 16:58:30,643 - INFO - app - app.py:105 - Loading client data from database
2025-07-08 16:58:30,761 - INFO - app - app.py:114 - Successfully loaded data for 1 clients
2025-07-08 16:58:30,762 - INFO - app - app.py:62 - Successfully loaded image: logo/logo_integrum.jpg
2025-07-08 16:58:30,766 - INFO - app - app.py:145 - Client selection: Kids Clinic India Limited, Plant: None, Type: Combined
2025-07-08 16:58:30,770 - INFO - app - app.py:156 - Date range selected: 2025-07-08 to 2025-07-08
2025-07-08 16:58:30,772 - INFO - app - app.py:184 - Created main application tabs
2025-07-08 16:58:30,772 - INFO - app - app.py:194 - Summary tab: Displaying data for Kids Clinic India Limited
2025-07-08 16:58:32,031 - INFO - app - app.py:209 - Successfully displayed generation vs consumption chart for Kids Clinic India Limited
2025-07-08 16:58:32,032 - INFO - app - app.py:266 - ToD Analysis tab: Displaying data for Kids Clinic India Limited
2025-07-08 16:59:44,061 - INFO - app - app.py:79 - Starting application initialization
2025-07-08 16:59:44,062 - INFO - app - app.py:81 - Page configuration completed
2025-07-08 16:59:44,063 - INFO - app - app.py:85 - Session state initialized
2025-07-08 16:59:44,063 - INFO - app - app.py:93 - User authenticated successfully
2025-07-08 16:59:44,065 - INFO - app - app.py:105 - Loading client data from database
2025-07-08 16:59:44,175 - INFO - app - app.py:114 - Successfully loaded data for 1 clients
2025-07-08 16:59:44,175 - INFO - app - app.py:62 - Successfully loaded image: logo/logo_integrum.jpg
2025-07-08 16:59:44,175 - INFO - app - app.py:145 - Client selection: Kids Clinic India Limited, Plant: None, Type: Combined
2025-07-08 16:59:44,175 - INFO - app - app.py:156 - Date range selected: 2025-07-08 to 2025-07-08
2025-07-08 16:59:44,182 - INFO - app - app.py:184 - Created main application tabs
2025-07-08 16:59:44,183 - INFO - app - app.py:194 - Summary tab: Displaying data for Kids Clinic India Limited
2025-07-08 16:59:44,943 - INFO - app - app.py:209 - Successfully displayed generation vs consumption chart for Kids Clinic India Limited
2025-07-08 16:59:46,013 - INFO - app - app.py:219 - Successfully displayed monthly banking settlement for Kids Clinic India Limited
2025-07-08 16:59:46,089 - INFO - app - app.py:230 - Successfully displayed ToD generation analysis for Kids Clinic India Limited
2025-07-08 16:59:46,151 - INFO - app - app.py:241 - Successfully displayed ToD consumption analysis for Kids Clinic India Limited
2025-07-08 16:59:46,236 - INFO - app - app.py:252 - Successfully displayed hourly generation vs consumption chart for Kids Clinic India Limited
2025-07-08 16:59:46,237 - INFO - app - app.py:266 - ToD Analysis tab: Displaying data for Kids Clinic India Limited
2025-07-08 17:00:54,518 - INFO - app - app.py:79 - Starting application initialization
2025-07-08 17:00:54,518 - INFO - app - app.py:81 - Page configuration completed
2025-07-08 17:00:54,518 - INFO - app - app.py:85 - Session state initialized
2025-07-08 17:00:54,518 - INFO - app - app.py:93 - User authenticated successfully
2025-07-08 17:00:54,518 - INFO - app - app.py:105 - Loading client data from database
2025-07-08 17:00:54,630 - INFO - app - app.py:114 - Successfully loaded data for 1 clients
2025-07-08 17:00:54,630 - INFO - app - app.py:62 - Successfully loaded image: logo/logo_integrum.jpg
2025-07-08 17:00:54,630 - INFO - app - app.py:145 - Client selection: Kids Clinic India Limited, Plant: None, Type: Combined
2025-07-08 17:00:54,630 - INFO - app - app.py:156 - Date range selected: 2025-07-08 to 2025-07-08
2025-07-08 17:00:54,630 - INFO - app - app.py:184 - Created main application tabs
2025-07-08 17:00:54,630 - INFO - app - app.py:194 - Summary tab: Displaying data for Kids Clinic India Limited
2025-07-08 17:00:55,481 - INFO - app - app.py:209 - Successfully displayed generation vs consumption chart for Kids Clinic India Limited
2025-07-08 17:00:56,546 - INFO - app - app.py:219 - Successfully displayed monthly banking settlement for Kids Clinic India Limited
2025-07-08 17:00:56,644 - INFO - app - app.py:230 - Successfully displayed ToD generation analysis for Kids Clinic India Limited
2025-07-08 17:00:56,711 - INFO - app - app.py:241 - Successfully displayed ToD consumption analysis for Kids Clinic India Limited
2025-07-08 17:00:56,797 - INFO - app - app.py:252 - Successfully displayed hourly generation vs consumption chart for Kids Clinic India Limited
2025-07-08 17:00:56,798 - INFO - app - app.py:266 - ToD Analysis tab: Displaying data for Kids Clinic India Limited
2025-07-08 17:01:18,929 - INFO - app - app.py:79 - Starting application initialization
2025-07-08 17:01:18,930 - INFO - app - app.py:81 - Page configuration completed
2025-07-08 17:01:18,932 - INFO - app - app.py:85 - Session state initialized
2025-07-08 17:01:18,932 - INFO - app - app.py:93 - User authenticated successfully
2025-07-08 17:01:18,932 - INFO - app - app.py:105 - Loading client data from database
2025-07-08 17:01:19,033 - INFO - app - app.py:114 - Successfully loaded data for 1 clients
2025-07-08 17:01:19,033 - INFO - app - app.py:62 - Successfully loaded image: logo/logo_integrum.jpg
2025-07-08 17:01:19,033 - INFO - app - app.py:145 - Client selection: Kids Clinic India Limited, Plant: None, Type: Combined
2025-07-08 17:01:19,033 - INFO - app - app.py:156 - Date range selected: 2025-07-08 to 2025-07-08
2025-07-08 17:01:19,033 - INFO - app - app.py:184 - Created main application tabs
2025-07-08 17:01:19,033 - INFO - app - app.py:194 - Summary tab: Displaying data for Kids Clinic India Limited
2025-07-08 17:01:19,884 - INFO - app - app.py:209 - Successfully displayed generation vs consumption chart for Kids Clinic India Limited
2025-07-08 17:01:20,990 - INFO - app - app.py:219 - Successfully displayed monthly banking settlement for Kids Clinic India Limited
2025-07-08 17:01:21,064 - INFO - app - app.py:230 - Successfully displayed ToD generation analysis for Kids Clinic India Limited
2025-07-08 17:01:21,147 - INFO - app - app.py:241 - Successfully displayed ToD consumption analysis for Kids Clinic India Limited
2025-07-08 17:01:21,230 - INFO - app - app.py:252 - Successfully displayed hourly generation vs consumption chart for Kids Clinic India Limited
2025-07-08 17:01:21,230 - INFO - app - app.py:266 - ToD Analysis tab: Displaying data for Kids Clinic India Limited
2025-07-08 17:01:40,980 - INFO - app - app.py:79 - Starting application initialization
2025-07-08 17:01:40,983 - INFO - app - app.py:81 - Page configuration completed
2025-07-08 17:01:40,984 - INFO - app - app.py:85 - Session state initialized
2025-07-08 17:01:40,984 - INFO - app - app.py:93 - User authenticated successfully
2025-07-08 17:01:40,987 - INFO - app - app.py:105 - Loading client data from database
2025-07-08 17:01:41,114 - INFO - app - app.py:114 - Successfully loaded data for 1 clients
2025-07-08 17:01:41,115 - INFO - app - app.py:62 - Successfully loaded image: logo/logo_integrum.jpg
2025-07-08 17:01:41,117 - INFO - app - app.py:145 - Client selection: Kids Clinic India Limited, Plant: None, Type: Combined
2025-07-08 17:01:41,118 - INFO - app - app.py:156 - Date range selected: 2025-07-08 to 2025-07-08
2025-07-08 17:01:41,123 - INFO - app - app.py:184 - Created main application tabs
2025-07-08 17:01:41,124 - INFO - app - app.py:194 - Summary tab: Displaying data for Kids Clinic India Limited
2025-07-08 17:01:42,001 - INFO - app - app.py:209 - Successfully displayed generation vs consumption chart for Kids Clinic India Limited
2025-07-08 17:01:43,059 - INFO - app - app.py:219 - Successfully displayed monthly banking settlement for Kids Clinic India Limited
2025-07-08 17:01:43,160 - INFO - app - app.py:230 - Successfully displayed ToD generation analysis for Kids Clinic India Limited
2025-07-08 17:01:43,235 - INFO - app - app.py:241 - Successfully displayed ToD consumption analysis for Kids Clinic India Limited
2025-07-08 17:01:43,333 - INFO - app - app.py:252 - Successfully displayed hourly generation vs consumption chart for Kids Clinic India Limited
2025-07-08 17:01:43,333 - INFO - app - app.py:266 - ToD Analysis tab: Displaying data for Kids Clinic India Limited
2025-07-08 17:02:07,219 - INFO - app - app.py:79 - Starting application initialization
2025-07-08 17:02:07,221 - INFO - app - app.py:81 - Page configuration completed
2025-07-08 17:02:07,222 - INFO - app - app.py:85 - Session state initialized
2025-07-08 17:02:07,222 - INFO - app - app.py:93 - User authenticated successfully
2025-07-08 17:02:07,223 - INFO - app - app.py:105 - Loading client data from database
2025-07-08 17:02:07,301 - INFO - app - app.py:114 - Successfully loaded data for 1 clients
2025-07-08 17:02:07,301 - INFO - app - app.py:62 - Successfully loaded image: logo/logo_integrum.jpg
2025-07-08 17:02:07,301 - INFO - app - app.py:145 - Client selection: Kids Clinic India Limited, Plant: None, Type: Combined
2025-07-08 17:02:07,301 - INFO - app - app.py:156 - Date range selected: 2025-07-08 to 2025-07-08
2025-07-08 17:02:07,301 - INFO - app - app.py:184 - Created main application tabs
2025-07-08 17:02:07,301 - INFO - app - app.py:194 - Summary tab: Displaying data for Kids Clinic India Limited
2025-07-08 17:02:08,221 - INFO - app - app.py:209 - Successfully displayed generation vs consumption chart for Kids Clinic India Limited
2025-07-08 17:02:09,224 - INFO - app - app.py:219 - Successfully displayed monthly banking settlement for Kids Clinic India Limited
2025-07-08 17:02:09,317 - INFO - app - app.py:230 - Successfully displayed ToD generation analysis for Kids Clinic India Limited
2025-07-08 17:02:09,425 - INFO - app - app.py:241 - Successfully displayed ToD consumption analysis for Kids Clinic India Limited
2025-07-08 17:02:09,508 - INFO - app - app.py:252 - Successfully displayed hourly generation vs consumption chart for Kids Clinic India Limited
2025-07-08 17:02:09,508 - INFO - app - app.py:266 - ToD Analysis tab: Displaying data for Kids Clinic India Limited
2025-07-08 17:02:31,773 - INFO - app - app.py:79 - Starting application initialization
2025-07-08 17:02:31,773 - INFO - app - app.py:81 - Page configuration completed
2025-07-08 17:02:31,774 - INFO - app - app.py:85 - Session state initialized
2025-07-08 17:02:31,774 - INFO - app - app.py:93 - User authenticated successfully
2025-07-08 17:02:31,775 - INFO - app - app.py:105 - Loading client data from database
2025-07-08 17:02:31,853 - INFO - app - app.py:114 - Successfully loaded data for 1 clients
2025-07-08 17:02:31,853 - INFO - app - app.py:62 - Successfully loaded image: logo/logo_integrum.jpg
2025-07-08 17:02:31,853 - INFO - app - app.py:145 - Client selection: Kids Clinic India Limited, Plant: None, Type: Combined
2025-07-08 17:02:31,868 - INFO - app - app.py:156 - Date range selected: 2025-07-08 to 2025-07-08
2025-07-08 17:02:31,871 - INFO - app - app.py:184 - Created main application tabs
2025-07-08 17:02:31,872 - INFO - app - app.py:194 - Summary tab: Displaying data for Kids Clinic India Limited
2025-07-08 17:02:32,702 - INFO - app - app.py:209 - Successfully displayed generation vs consumption chart for Kids Clinic India Limited
2025-07-08 17:02:33,699 - INFO - app - app.py:219 - Successfully displayed monthly banking settlement for Kids Clinic India Limited
2025-07-08 17:02:33,781 - INFO - app - app.py:230 - Successfully displayed ToD generation analysis for Kids Clinic India Limited
2025-07-08 17:02:33,856 - INFO - app - app.py:241 - Successfully displayed ToD consumption analysis for Kids Clinic India Limited
2025-07-08 17:02:33,961 - INFO - app - app.py:252 - Successfully displayed hourly generation vs consumption chart for Kids Clinic India Limited
2025-07-08 17:02:33,961 - INFO - app - app.py:266 - ToD Analysis tab: Displaying data for Kids Clinic India Limited
2025-07-08 17:03:03,870 - INFO - app - app.py:79 - Starting application initialization
2025-07-08 17:03:03,873 - INFO - app - app.py:81 - Page configuration completed
2025-07-08 17:03:03,873 - INFO - app - app.py:85 - Session state initialized
2025-07-08 17:03:03,873 - INFO - app - app.py:93 - User authenticated successfully
2025-07-08 17:03:03,873 - INFO - app - app.py:105 - Loading client data from database
2025-07-08 17:03:03,968 - INFO - app - app.py:114 - Successfully loaded data for 1 clients
2025-07-08 17:03:03,968 - INFO - app - app.py:62 - Successfully loaded image: logo/logo_integrum.jpg
2025-07-08 17:03:03,971 - INFO - app - app.py:145 - Client selection: Kids Clinic India Limited, Plant: None, Type: Combined
2025-07-08 17:03:03,971 - INFO - app - app.py:156 - Date range selected: 2025-05-01 to 2025-05-01
2025-07-08 17:03:03,971 - INFO - app - app.py:184 - Created main application tabs
2025-07-08 17:03:03,971 - INFO - app - app.py:194 - Summary tab: Displaying data for Kids Clinic India Limited
2025-07-08 17:03:04,837 - INFO - app - app.py:79 - Starting application initialization
2025-07-08 17:03:04,837 - INFO - app - app.py:81 - Page configuration completed
2025-07-08 17:03:04,837 - INFO - app - app.py:85 - Session state initialized
2025-07-08 17:03:04,837 - INFO - app - app.py:93 - User authenticated successfully
2025-07-08 17:03:04,853 - INFO - app - app.py:105 - Loading client data from database
2025-07-08 17:03:05,055 - INFO - app - app.py:114 - Successfully loaded data for 1 clients
2025-07-08 17:03:05,055 - INFO - app - app.py:62 - Successfully loaded image: logo/logo_integrum.jpg
2025-07-08 17:03:05,062 - INFO - app - app.py:145 - Client selection: Kids Clinic India Limited, Plant: None, Type: Combined
2025-07-08 17:03:05,062 - INFO - app - app.py:156 - Date range selected: 2025-05-01 to 2025-05-31
2025-07-08 17:03:05,073 - INFO - app - app.py:184 - Created main application tabs
2025-07-08 17:03:05,073 - INFO - app - app.py:194 - Summary tab: Displaying data for Kids Clinic India Limited
2025-07-08 17:03:05,923 - INFO - app - app.py:209 - Successfully displayed generation vs consumption chart for Kids Clinic India Limited
2025-07-08 17:03:07,608 - INFO - app - app.py:219 - Successfully displayed monthly banking settlement for Kids Clinic India Limited
2025-07-08 17:03:07,972 - INFO - app - app.py:230 - Successfully displayed ToD generation analysis for Kids Clinic India Limited
2025-07-08 17:03:08,334 - INFO - app - app.py:241 - Successfully displayed ToD consumption analysis for Kids Clinic India Limited
2025-07-08 17:03:08,758 - INFO - app - app.py:252 - Successfully displayed hourly generation vs consumption chart for Kids Clinic India Limited
2025-07-08 17:03:08,758 - INFO - app - app.py:266 - ToD Analysis tab: Displaying data for Kids Clinic India Limited
2025-07-08 17:03:17,147 - INFO - app - app.py:79 - Starting application initialization
2025-07-08 17:03:17,147 - INFO - app - app.py:81 - Page configuration completed
2025-07-08 17:03:17,147 - INFO - app - app.py:85 - Session state initialized
2025-07-08 17:03:17,155 - INFO - app - app.py:93 - User authenticated successfully
2025-07-08 17:03:17,157 - INFO - app - app.py:105 - Loading client data from database
2025-07-08 17:03:17,239 - INFO - app - app.py:114 - Successfully loaded data for 1 clients
2025-07-08 17:03:17,240 - INFO - app - app.py:62 - Successfully loaded image: logo/logo_integrum.jpg
2025-07-08 17:03:17,242 - INFO - app - app.py:145 - Client selection: Kids Clinic India Limited, Plant: None, Type: Combined
2025-07-08 17:03:17,243 - INFO - app - app.py:156 - Date range selected: 2025-05-01 to 2025-05-31
2025-07-08 17:03:17,247 - INFO - app - app.py:184 - Created main application tabs
2025-07-08 17:03:17,247 - INFO - app - app.py:194 - Summary tab: Displaying data for Kids Clinic India Limited
2025-07-08 17:03:18,079 - INFO - app - app.py:209 - Successfully displayed generation vs consumption chart for Kids Clinic India Limited
2025-07-08 17:03:19,133 - INFO - app - app.py:219 - Successfully displayed monthly banking settlement for Kids Clinic India Limited
2025-07-08 17:03:19,312 - INFO - app - app.py:230 - Successfully displayed ToD generation analysis for Kids Clinic India Limited
2025-07-08 17:03:19,512 - INFO - app - app.py:241 - Successfully displayed ToD consumption analysis for Kids Clinic India Limited
2025-07-08 17:03:19,741 - INFO - app - app.py:252 - Successfully displayed hourly generation vs consumption chart for Kids Clinic India Limited
2025-07-08 17:03:19,742 - INFO - app - app.py:266 - ToD Analysis tab: Displaying data for Kids Clinic India Limited
2025-07-08 17:08:24,037 - INFO - app - app.py:79 - Starting application initialization
2025-07-08 17:08:24,037 - INFO - app - app.py:81 - Page configuration completed
2025-07-08 17:08:24,037 - INFO - app - app.py:85 - Session state initialized
2025-07-08 17:08:24,037 - INFO - app - app.py:93 - User authenticated successfully
2025-07-08 17:08:24,037 - INFO - app - app.py:105 - Loading client data from database
2025-07-08 17:08:24,169 - INFO - app - app.py:114 - Successfully loaded data for 1 clients
2025-07-08 17:08:24,170 - INFO - app - app.py:62 - Successfully loaded image: logo/logo_integrum.jpg
2025-07-08 17:08:24,172 - INFO - app - app.py:145 - Client selection: Kids Clinic India Limited, Plant: None, Type: Combined
2025-07-08 17:08:24,173 - INFO - app - app.py:156 - Date range selected: 2025-05-01 to 2025-05-31
2025-07-08 17:08:24,176 - INFO - app - app.py:184 - Created main application tabs
2025-07-08 17:08:24,179 - INFO - app - app.py:194 - Summary tab: Displaying data for Kids Clinic India Limited
2025-07-08 17:08:25,050 - INFO - app - app.py:209 - Successfully displayed generation vs consumption chart for Kids Clinic India Limited
2025-07-08 17:08:26,018 - INFO - app - app.py:219 - Successfully displayed monthly banking settlement for Kids Clinic India Limited
2025-07-08 17:08:26,310 - INFO - app - app.py:230 - Successfully displayed ToD generation analysis for Kids Clinic India Limited
2025-07-08 17:08:26,523 - INFO - app - app.py:241 - Successfully displayed ToD consumption analysis for Kids Clinic India Limited
2025-07-08 17:08:26,778 - INFO - app - app.py:252 - Successfully displayed hourly generation vs consumption chart for Kids Clinic India Limited
2025-07-08 17:08:26,778 - INFO - app - app.py:266 - ToD Analysis tab: Displaying data for Kids Clinic India Limited
2025-07-08 17:08:38,761 - INFO - app - app.py:79 - Starting application initialization
2025-07-08 17:08:38,762 - INFO - app - app.py:81 - Page configuration completed
2025-07-08 17:08:38,763 - INFO - app - app.py:85 - Session state initialized
2025-07-08 17:08:38,763 - INFO - app - app.py:93 - User authenticated successfully
2025-07-08 17:08:38,765 - INFO - app - app.py:105 - Loading client data from database
2025-07-08 17:08:38,880 - INFO - app - app.py:114 - Successfully loaded data for 1 clients
2025-07-08 17:08:38,880 - INFO - app - app.py:62 - Successfully loaded image: logo/logo_integrum.jpg
2025-07-08 17:08:38,880 - INFO - app - app.py:145 - Client selection: Kids Clinic India Limited, Plant: None, Type: Combined
2025-07-08 17:08:38,880 - INFO - app - app.py:156 - Date range selected: 2025-05-01 to 2025-05-31
2025-07-08 17:08:38,891 - INFO - app - app.py:184 - Created main application tabs
2025-07-08 17:08:38,891 - INFO - app - app.py:194 - Summary tab: Displaying data for Kids Clinic India Limited
2025-07-08 17:08:39,774 - INFO - app - app.py:209 - Successfully displayed generation vs consumption chart for Kids Clinic India Limited
2025-07-08 17:08:40,761 - INFO - app - app.py:219 - Successfully displayed monthly banking settlement for Kids Clinic India Limited
2025-07-08 17:08:41,023 - INFO - app - app.py:230 - Successfully displayed ToD generation analysis for Kids Clinic India Limited
2025-07-08 17:08:41,232 - INFO - app - app.py:241 - Successfully displayed ToD consumption analysis for Kids Clinic India Limited
2025-07-08 17:08:41,493 - INFO - app - app.py:252 - Successfully displayed hourly generation vs consumption chart for Kids Clinic India Limited
2025-07-08 17:08:41,495 - INFO - app - app.py:266 - ToD Analysis tab: Displaying data for Kids Clinic India Limited
2025-07-08 17:09:42,666 - INFO - app - app.py:79 - Starting application initialization
2025-07-08 17:09:42,667 - INFO - app - app.py:81 - Page configuration completed
2025-07-08 17:09:42,667 - INFO - app - app.py:85 - Session state initialized
2025-07-08 17:09:42,668 - INFO - app - app.py:93 - User authenticated successfully
2025-07-08 17:09:42,669 - INFO - app - app.py:105 - Loading client data from database
2025-07-08 17:09:42,797 - INFO - app - app.py:114 - Successfully loaded data for 1 clients
2025-07-08 17:09:42,798 - INFO - app - app.py:62 - Successfully loaded image: logo/logo_integrum.jpg
2025-07-08 17:09:42,800 - INFO - app - app.py:145 - Client selection: Kids Clinic India Limited, Plant: None, Type: Combined
2025-07-08 17:09:42,803 - INFO - app - app.py:156 - Date range selected: 2025-05-01 to 2025-05-31
2025-07-08 17:09:42,808 - INFO - app - app.py:184 - Created main application tabs
2025-07-08 17:09:42,809 - INFO - app - app.py:194 - Summary tab: Displaying data for Kids Clinic India Limited
2025-07-08 17:09:43,736 - INFO - app - app.py:209 - Successfully displayed generation vs consumption chart for Kids Clinic India Limited
2025-07-08 17:09:44,972 - INFO - app - app.py:219 - Successfully displayed monthly banking settlement for Kids Clinic India Limited
2025-07-08 17:09:45,261 - INFO - app - app.py:230 - Successfully displayed ToD generation analysis for Kids Clinic India Limited
2025-07-08 17:09:45,541 - INFO - app - app.py:241 - Successfully displayed ToD consumption analysis for Kids Clinic India Limited
2025-07-08 17:09:45,825 - INFO - app - app.py:252 - Successfully displayed hourly generation vs consumption chart for Kids Clinic India Limited
2025-07-08 17:09:45,825 - INFO - app - app.py:266 - ToD Analysis tab: Displaying data for Kids Clinic India Limited
2025-07-08 17:10:12,345 - INFO - app - app.py:79 - Starting application initialization
2025-07-08 17:10:12,347 - INFO - app - app.py:81 - Page configuration completed
2025-07-08 17:10:12,348 - INFO - app - app.py:85 - Session state initialized
2025-07-08 17:10:12,348 - INFO - app - app.py:93 - User authenticated successfully
2025-07-08 17:10:12,348 - INFO - app - app.py:105 - Loading client data from database
2025-07-08 17:10:12,453 - INFO - app - app.py:114 - Successfully loaded data for 1 clients
2025-07-08 17:10:12,453 - INFO - app - app.py:62 - Successfully loaded image: logo/logo_integrum.jpg
2025-07-08 17:10:12,453 - INFO - app - app.py:145 - Client selection: Kids Clinic India Limited, Plant: None, Type: Combined
2025-07-08 17:10:12,453 - INFO - app - app.py:156 - Date range selected: 2025-05-01 to 2025-05-31
2025-07-08 17:10:12,464 - INFO - app - app.py:184 - Created main application tabs
2025-07-08 17:10:12,464 - INFO - app - app.py:194 - Summary tab: Displaying data for Kids Clinic India Limited
2025-07-08 17:10:13,320 - INFO - app - app.py:209 - Successfully displayed generation vs consumption chart for Kids Clinic India Limited
2025-07-08 17:10:14,359 - INFO - app - app.py:219 - Successfully displayed monthly banking settlement for Kids Clinic India Limited
2025-07-08 17:10:14,618 - INFO - app - app.py:230 - Successfully displayed ToD generation analysis for Kids Clinic India Limited
2025-07-08 17:10:14,827 - INFO - app - app.py:241 - Successfully displayed ToD consumption analysis for Kids Clinic India Limited
2025-07-08 17:10:15,088 - INFO - app - app.py:252 - Successfully displayed hourly generation vs consumption chart for Kids Clinic India Limited
2025-07-08 17:10:15,088 - INFO - app - app.py:266 - ToD Analysis tab: Displaying data for Kids Clinic India Limited
2025-07-08 17:10:43,832 - INFO - app - app.py:79 - Starting application initialization
2025-07-08 17:10:43,872 - INFO - app - app.py:81 - Page configuration completed
2025-07-08 17:10:43,872 - INFO - app - app.py:85 - Session state initialized
2025-07-08 17:10:43,872 - INFO - app - app.py:89 - User not authenticated, showing login form
2025-07-08 17:10:47,124 - INFO - app - app.py:79 - Starting application initialization
2025-07-08 17:10:47,124 - INFO - app - app.py:81 - Page configuration completed
2025-07-08 17:10:47,124 - INFO - app - app.py:85 - Session state initialized
2025-07-08 17:10:47,124 - INFO - app - app.py:89 - User not authenticated, showing login form
2025-07-08 17:10:48,605 - INFO - app - app.py:79 - Starting application initialization
2025-07-08 17:10:48,605 - INFO - app - app.py:81 - Page configuration completed
2025-07-08 17:10:48,605 - INFO - app - app.py:85 - Session state initialized
2025-07-08 17:10:48,605 - INFO - app - app.py:93 - User authenticated successfully
2025-07-08 17:10:48,605 - INFO - app - app.py:105 - Loading client data from database
2025-07-08 17:10:49,104 - INFO - app - app.py:114 - Successfully loaded data for 1 clients
2025-07-08 17:10:49,104 - INFO - app - app.py:62 - Successfully loaded image: logo/logo_integrum.jpg
2025-07-08 17:10:49,104 - INFO - app - app.py:145 - Client selection: None, Plant: None, Type: None
2025-07-08 17:10:49,113 - INFO - app - app.py:156 - Date range selected: 2025-07-08 to 2025-07-08
2025-07-08 17:10:49,115 - INFO - app - app.py:316 - No client selected, showing welcome message
2025-07-08 17:10:52,080 - INFO - app - app.py:79 - Starting application initialization
2025-07-08 17:10:52,080 - INFO - app - app.py:81 - Page configuration completed
2025-07-08 17:10:52,080 - INFO - app - app.py:85 - Session state initialized
2025-07-08 17:10:52,080 - INFO - app - app.py:93 - User authenticated successfully
2025-07-08 17:10:52,084 - INFO - app - app.py:105 - Loading client data from database
2025-07-08 17:10:52,168 - INFO - app - app.py:114 - Successfully loaded data for 1 clients
2025-07-08 17:10:52,169 - INFO - app - app.py:62 - Successfully loaded image: logo/logo_integrum.jpg
2025-07-08 17:10:52,174 - INFO - app - app.py:145 - Client selection: Kids Clinic India Limited, Plant: None, Type: Combined
2025-07-08 17:10:52,176 - INFO - app - app.py:156 - Date range selected: 2025-07-08 to 2025-07-08
2025-07-08 17:10:52,179 - INFO - app - app.py:184 - Created main application tabs
2025-07-08 17:10:52,181 - INFO - app - app.py:194 - Summary tab: Displaying data for Kids Clinic India Limited
2025-07-08 17:10:53,365 - INFO - app - app.py:209 - Successfully displayed generation vs consumption chart for Kids Clinic India Limited
2025-07-08 17:10:55,153 - INFO - app - app.py:219 - Successfully displayed monthly banking settlement for Kids Clinic India Limited
2025-07-08 17:10:55,224 - INFO - app - app.py:230 - Successfully displayed ToD generation analysis for Kids Clinic India Limited
2025-07-08 17:10:55,302 - INFO - app - app.py:241 - Successfully displayed ToD consumption analysis for Kids Clinic India Limited
2025-07-08 17:10:55,424 - INFO - app - app.py:252 - Successfully displayed hourly generation vs consumption chart for Kids Clinic India Limited
2025-07-08 17:10:55,424 - INFO - app - app.py:266 - ToD Analysis tab: Displaying data for Kids Clinic India Limited
2025-07-08 17:11:08,485 - INFO - app - app.py:79 - Starting application initialization
2025-07-08 17:11:08,487 - INFO - app - app.py:81 - Page configuration completed
2025-07-08 17:11:08,488 - INFO - app - app.py:85 - Session state initialized
2025-07-08 17:11:08,488 - INFO - app - app.py:89 - User not authenticated, showing login form
2025-07-08 17:13:39,108 - INFO - app - app.py:79 - Starting application initialization
2025-07-08 17:13:39,108 - INFO - app - app.py:81 - Page configuration completed
2025-07-08 17:13:39,108 - INFO - app - app.py:85 - Session state initialized
2025-07-08 17:13:39,108 - INFO - app - app.py:93 - User authenticated successfully
2025-07-08 17:13:39,108 - INFO - app - app.py:105 - Loading client data from database
2025-07-08 17:13:39,217 - INFO - app - app.py:114 - Successfully loaded data for 1 clients
2025-07-08 17:13:39,218 - INFO - app - app.py:62 - Successfully loaded image: logo/logo_integrum.jpg
2025-07-08 17:13:39,220 - INFO - app - app.py:145 - Client selection: Kids Clinic India Limited, Plant: None, Type: Combined
2025-07-08 17:13:39,220 - INFO - app - app.py:156 - Date range selected: 2025-05-01 to 2025-05-31
2025-07-08 17:13:39,224 - INFO - app - app.py:184 - Created main application tabs
2025-07-08 17:13:39,224 - INFO - app - app.py:194 - Summary tab: Displaying data for Kids Clinic India Limited
2025-07-08 17:13:40,095 - INFO - app - app.py:209 - Successfully displayed generation vs consumption chart for Kids Clinic India Limited
2025-07-08 17:13:41,151 - INFO - app - app.py:219 - Successfully displayed monthly banking settlement for Kids Clinic India Limited
2025-07-08 17:13:41,374 - INFO - app - app.py:230 - Successfully displayed ToD generation analysis for Kids Clinic India Limited
2025-07-08 17:13:41,592 - INFO - app - app.py:241 - Successfully displayed ToD consumption analysis for Kids Clinic India Limited
2025-07-08 17:13:41,841 - INFO - app - app.py:252 - Successfully displayed hourly generation vs consumption chart for Kids Clinic India Limited
2025-07-08 17:13:43,845 - INFO - app - app.py:263 - Successfully displayed unit-wise analysis for Kids Clinic India Limited
2025-07-08 17:13:43,845 - INFO - app - app.py:275 - ToD Analysis tab: Displaying data for Kids Clinic India Limited
2025-07-08 17:17:19,471 - INFO - app - app.py:79 - Starting application initialization
2025-07-08 17:17:19,473 - INFO - app - app.py:81 - Page configuration completed
2025-07-08 17:17:19,473 - INFO - app - app.py:85 - Session state initialized
2025-07-08 17:17:19,474 - INFO - app - app.py:93 - User authenticated successfully
2025-07-08 17:17:19,474 - INFO - app - app.py:105 - Loading client data from database
2025-07-08 17:17:19,589 - INFO - app - app.py:114 - Successfully loaded data for 1 clients
2025-07-08 17:17:19,589 - INFO - app - app.py:62 - Successfully loaded image: logo/logo_integrum.jpg
2025-07-08 17:17:19,589 - INFO - app - app.py:145 - Client selection: Kids Clinic India Limited, Plant: None, Type: Combined
2025-07-08 17:17:19,589 - INFO - app - app.py:156 - Date range selected: 2025-05-01 to 2025-05-31
2025-07-08 17:17:19,600 - INFO - app - app.py:184 - Created main application tabs
2025-07-08 17:17:19,602 - INFO - app - app.py:194 - Summary tab: Displaying data for Kids Clinic India Limited
2025-07-08 17:17:20,482 - INFO - app - app.py:209 - Successfully displayed generation vs consumption chart for Kids Clinic India Limited
2025-07-08 17:17:21,558 - INFO - app - app.py:219 - Successfully displayed monthly banking settlement for Kids Clinic India Limited
2025-07-08 17:17:21,851 - INFO - app - app.py:230 - Successfully displayed ToD generation analysis for Kids Clinic India Limited
2025-07-08 17:17:22,102 - INFO - app - app.py:241 - Successfully displayed ToD consumption analysis for Kids Clinic India Limited
2025-07-08 17:17:22,353 - INFO - app - app.py:252 - Successfully displayed hourly generation vs consumption chart for Kids Clinic India Limited
2025-07-08 17:17:22,931 - INFO - app - app.py:263 - Successfully displayed unit-wise analysis for Kids Clinic India Limited
2025-07-08 17:17:22,933 - INFO - app - app.py:275 - ToD Analysis tab: Displaying data for Kids Clinic India Limited
2025-07-08 17:17:31,517 - INFO - app - app.py:79 - Starting application initialization
2025-07-08 17:17:31,519 - INFO - app - app.py:81 - Page configuration completed
2025-07-08 17:17:31,519 - INFO - app - app.py:85 - Session state initialized
2025-07-08 17:17:31,520 - INFO - app - app.py:93 - User authenticated successfully
2025-07-08 17:17:31,521 - INFO - app - app.py:105 - Loading client data from database
2025-07-08 17:17:31,629 - INFO - app - app.py:114 - Successfully loaded data for 1 clients
2025-07-08 17:17:31,629 - INFO - app - app.py:62 - Successfully loaded image: logo/logo_integrum.jpg
2025-07-08 17:17:31,631 - INFO - app - app.py:145 - Client selection: Kids Clinic India Limited, Plant: None, Type: Combined
2025-07-08 17:17:31,635 - INFO - app - app.py:156 - Date range selected: 2025-05-01 to 2025-05-31
2025-07-08 17:17:31,640 - INFO - app - app.py:184 - Created main application tabs
2025-07-08 17:17:31,642 - INFO - app - app.py:194 - Summary tab: Displaying data for Kids Clinic India Limited
2025-07-08 17:17:32,484 - INFO - app - app.py:209 - Successfully displayed generation vs consumption chart for Kids Clinic India Limited
2025-07-08 17:17:33,641 - INFO - app - app.py:219 - Successfully displayed monthly banking settlement for Kids Clinic India Limited
2025-07-08 17:17:33,892 - INFO - app - app.py:230 - Successfully displayed ToD generation analysis for Kids Clinic India Limited
2025-07-08 17:17:34,134 - INFO - app - app.py:241 - Successfully displayed ToD consumption analysis for Kids Clinic India Limited
2025-07-08 17:17:34,393 - INFO - app - app.py:252 - Successfully displayed hourly generation vs consumption chart for Kids Clinic India Limited
2025-07-08 17:17:36,162 - INFO - app - app.py:263 - Successfully displayed unit-wise analysis for Kids Clinic India Limited
2025-07-08 17:17:36,162 - INFO - app - app.py:275 - ToD Analysis tab: Displaying data for Kids Clinic India Limited
2025-07-08 17:18:11,653 - INFO - app - app.py:79 - Starting application initialization
2025-07-08 17:18:11,653 - INFO - app - app.py:81 - Page configuration completed
2025-07-08 17:18:11,655 - INFO - app - app.py:85 - Session state initialized
2025-07-08 17:18:11,655 - INFO - app - app.py:93 - User authenticated successfully
2025-07-08 17:18:11,657 - INFO - app - app.py:105 - Loading client data from database
2025-07-08 17:18:11,774 - INFO - app - app.py:114 - Successfully loaded data for 1 clients
2025-07-08 17:18:11,775 - INFO - app - app.py:62 - Successfully loaded image: logo/logo_integrum.jpg
2025-07-08 17:18:11,778 - INFO - app - app.py:145 - Client selection: Kids Clinic India Limited, Plant: None, Type: Combined
2025-07-08 17:18:11,779 - INFO - app - app.py:156 - Date range selected: 2025-05-01 to 2025-05-31
2025-07-08 17:18:11,783 - INFO - app - app.py:184 - Created main application tabs
2025-07-08 17:18:11,784 - INFO - app - app.py:194 - Summary tab: Displaying data for Kids Clinic India Limited
2025-07-08 17:18:12,657 - INFO - app - app.py:209 - Successfully displayed generation vs consumption chart for Kids Clinic India Limited
2025-07-08 17:18:13,673 - INFO - app - app.py:219 - Successfully displayed monthly banking settlement for Kids Clinic India Limited
2025-07-08 17:18:13,900 - INFO - app - app.py:230 - Successfully displayed ToD generation analysis for Kids Clinic India Limited
2025-07-08 17:18:14,146 - INFO - app - app.py:241 - Successfully displayed ToD consumption analysis for Kids Clinic India Limited
2025-07-08 17:18:14,440 - INFO - app - app.py:252 - Successfully displayed hourly generation vs consumption chart for Kids Clinic India Limited
2025-07-08 17:18:15,017 - INFO - app - app.py:263 - Successfully displayed unit-wise analysis for Kids Clinic India Limited
2025-07-08 17:18:15,017 - INFO - app - app.py:275 - ToD Analysis tab: Displaying data for Kids Clinic India Limited
2025-07-08 17:18:26,074 - INFO - app - app.py:79 - Starting application initialization
2025-07-08 17:18:26,075 - INFO - app - app.py:81 - Page configuration completed
2025-07-08 17:18:26,076 - INFO - app - app.py:85 - Session state initialized
2025-07-08 17:18:26,076 - INFO - app - app.py:93 - User authenticated successfully
2025-07-08 17:18:26,077 - INFO - app - app.py:105 - Loading client data from database
2025-07-08 17:18:26,176 - INFO - app - app.py:114 - Successfully loaded data for 1 clients
2025-07-08 17:18:26,176 - INFO - app - app.py:62 - Successfully loaded image: logo/logo_integrum.jpg
2025-07-08 17:18:26,178 - INFO - app - app.py:145 - Client selection: Kids Clinic India Limited, Plant: None, Type: Combined
2025-07-08 17:18:26,179 - INFO - app - app.py:156 - Date range selected: 2025-05-01 to 2025-05-31
2025-07-08 17:18:26,182 - INFO - app - app.py:184 - Created main application tabs
2025-07-08 17:18:26,183 - INFO - app - app.py:194 - Summary tab: Displaying data for Kids Clinic India Limited
2025-07-08 17:18:27,087 - INFO - app - app.py:209 - Successfully displayed generation vs consumption chart for Kids Clinic India Limited
2025-07-08 17:18:28,061 - INFO - app - app.py:219 - Successfully displayed monthly banking settlement for Kids Clinic India Limited
2025-07-08 17:18:28,312 - INFO - app - app.py:230 - Successfully displayed ToD generation analysis for Kids Clinic India Limited
2025-07-08 17:18:28,663 - INFO - app - app.py:241 - Successfully displayed ToD consumption analysis for Kids Clinic India Limited
2025-07-08 17:18:28,964 - INFO - app - app.py:252 - Successfully displayed hourly generation vs consumption chart for Kids Clinic India Limited
2025-07-08 17:18:29,453 - INFO - app - app.py:263 - Successfully displayed unit-wise analysis for Kids Clinic India Limited
2025-07-08 17:18:29,453 - INFO - app - app.py:275 - ToD Analysis tab: Displaying data for Kids Clinic India Limited
2025-07-08 17:18:40,052 - INFO - app - app.py:79 - Starting application initialization
2025-07-08 17:18:40,053 - INFO - app - app.py:81 - Page configuration completed
2025-07-08 17:18:40,054 - INFO - app - app.py:85 - Session state initialized
2025-07-08 17:18:40,055 - INFO - app - app.py:93 - User authenticated successfully
2025-07-08 17:18:40,055 - INFO - app - app.py:105 - Loading client data from database
2025-07-08 17:18:40,209 - INFO - app - app.py:114 - Successfully loaded data for 1 clients
2025-07-08 17:18:40,209 - INFO - app - app.py:62 - Successfully loaded image: logo/logo_integrum.jpg
2025-07-08 17:18:40,212 - INFO - app - app.py:145 - Client selection: Kids Clinic India Limited, Plant: None, Type: Combined
2025-07-08 17:18:40,213 - INFO - app - app.py:156 - Date range selected: 2025-05-01 to 2025-05-31
2025-07-08 17:18:40,216 - INFO - app - app.py:184 - Created main application tabs
2025-07-08 17:18:40,217 - INFO - app - app.py:194 - Summary tab: Displaying data for Kids Clinic India Limited
2025-07-08 17:18:41,158 - INFO - app - app.py:209 - Successfully displayed generation vs consumption chart for Kids Clinic India Limited
2025-07-08 17:18:42,138 - INFO - app - app.py:219 - Successfully displayed monthly banking settlement for Kids Clinic India Limited
2025-07-08 17:18:42,375 - INFO - app - app.py:230 - Successfully displayed ToD generation analysis for Kids Clinic India Limited
2025-07-08 17:18:42,592 - INFO - app - app.py:241 - Successfully displayed ToD consumption analysis for Kids Clinic India Limited
2025-07-08 17:18:42,947 - INFO - app - app.py:252 - Successfully displayed hourly generation vs consumption chart for Kids Clinic India Limited
2025-07-08 17:18:43,402 - INFO - app - app.py:263 - Successfully displayed unit-wise analysis for Kids Clinic India Limited
2025-07-08 17:18:43,403 - INFO - app - app.py:275 - ToD Analysis tab: Displaying data for Kids Clinic India Limited
2025-07-08 17:19:04,091 - INFO - app - app.py:79 - Starting application initialization
2025-07-08 17:19:04,091 - INFO - app - app.py:81 - Page configuration completed
2025-07-08 17:19:04,105 - INFO - app - app.py:85 - Session state initialized
2025-07-08 17:19:04,105 - INFO - app - app.py:93 - User authenticated successfully
2025-07-08 17:19:04,106 - INFO - app - app.py:105 - Loading client data from database
2025-07-08 17:19:04,209 - INFO - app - app.py:114 - Successfully loaded data for 1 clients
2025-07-08 17:19:04,210 - INFO - app - app.py:62 - Successfully loaded image: logo/logo_integrum.jpg
2025-07-08 17:19:04,211 - INFO - app - app.py:145 - Client selection: Kids Clinic India Limited, Plant: None, Type: Combined
2025-07-08 17:19:04,212 - INFO - app - app.py:156 - Date range selected: 2025-05-01 to 2025-05-31
2025-07-08 17:19:04,216 - INFO - app - app.py:184 - Created main application tabs
2025-07-08 17:19:04,216 - INFO - app - app.py:194 - Summary tab: Displaying data for Kids Clinic India Limited
2025-07-08 17:19:05,094 - INFO - app - app.py:209 - Successfully displayed generation vs consumption chart for Kids Clinic India Limited
2025-07-08 17:19:06,149 - INFO - app - app.py:219 - Successfully displayed monthly banking settlement for Kids Clinic India Limited
2025-07-08 17:19:06,426 - INFO - app - app.py:230 - Successfully displayed ToD generation analysis for Kids Clinic India Limited
2025-07-08 17:19:06,733 - INFO - app - app.py:241 - Successfully displayed ToD consumption analysis for Kids Clinic India Limited
2025-07-08 17:19:07,000 - INFO - app - app.py:252 - Successfully displayed hourly generation vs consumption chart for Kids Clinic India Limited
2025-07-08 17:19:07,484 - INFO - app - app.py:263 - Successfully displayed unit-wise analysis for Kids Clinic India Limited
2025-07-08 17:19:07,484 - INFO - app - app.py:275 - ToD Analysis tab: Displaying data for Kids Clinic India Limited
2025-07-08 17:19:15,469 - INFO - app - app.py:80 - Starting application initialization
2025-07-08 17:19:15,469 - INFO - app - app.py:82 - Page configuration completed
2025-07-08 17:19:15,469 - INFO - app - app.py:86 - Session state initialized
2025-07-08 17:19:15,469 - INFO - app - app.py:94 - User authenticated successfully
2025-07-08 17:19:15,469 - INFO - app - app.py:106 - Loading client data from database
2025-07-08 17:19:15,600 - INFO - app - app.py:115 - Successfully loaded data for 1 clients
2025-07-08 17:19:15,601 - INFO - app - app.py:63 - Successfully loaded image: logo/logo_integrum.jpg
2025-07-08 17:19:15,604 - INFO - app - app.py:146 - Client selection: Kids Clinic India Limited, Plant: None, Type: Combined
2025-07-08 17:19:15,605 - INFO - app - app.py:157 - Date range selected: 2025-05-01 to 2025-05-31
2025-07-08 17:19:15,611 - INFO - app - app.py:185 - Created main application tabs
2025-07-08 17:19:15,612 - INFO - app - app.py:195 - Summary tab: Displaying data for Kids Clinic India Limited
2025-07-08 17:19:16,502 - INFO - app - app.py:210 - Successfully displayed generation vs consumption chart for Kids Clinic India Limited
2025-07-08 17:19:18,140 - INFO - app - app.py:220 - Successfully displayed monthly banking settlement for Kids Clinic India Limited
2025-07-08 17:19:18,395 - INFO - app - app.py:231 - Successfully displayed ToD generation analysis for Kids Clinic India Limited
2025-07-08 17:19:18,630 - INFO - app - app.py:242 - Successfully displayed ToD consumption analysis for Kids Clinic India Limited
2025-07-08 17:19:18,906 - INFO - app - app.py:253 - Successfully displayed hourly generation vs consumption chart for Kids Clinic India Limited
2025-07-08 17:19:19,378 - INFO - app - app.py:264 - Successfully displayed unit-wise analysis for Kids Clinic India Limited
2025-07-08 17:19:19,378 - INFO - app - app.py:276 - ToD Analysis tab: Displaying data for Kids Clinic India Limited
2025-07-08 17:19:28,099 - INFO - app - app.py:80 - Starting application initialization
2025-07-08 17:19:28,101 - INFO - app - app.py:82 - Page configuration completed
2025-07-08 17:19:28,101 - INFO - app - app.py:86 - Session state initialized
2025-07-08 17:19:28,101 - INFO - app - app.py:94 - User authenticated successfully
2025-07-08 17:19:28,101 - INFO - app - app.py:106 - Loading client data from database
2025-07-08 17:19:28,229 - INFO - app - app.py:115 - Successfully loaded data for 1 clients
2025-07-08 17:19:28,231 - INFO - app - app.py:63 - Successfully loaded image: logo/logo_integrum.jpg
2025-07-08 17:19:28,232 - INFO - app - app.py:146 - Client selection: Kids Clinic India Limited, Plant: None, Type: Combined
2025-07-08 17:19:28,233 - INFO - app - app.py:157 - Date range selected: 2025-05-01 to 2025-05-31
2025-07-08 17:19:28,233 - INFO - app - app.py:185 - Created main application tabs
2025-07-08 17:19:28,233 - INFO - app - app.py:195 - Summary tab: Displaying data for Kids Clinic India Limited
2025-07-08 17:19:29,129 - INFO - app - app.py:210 - Successfully displayed generation vs consumption chart for Kids Clinic India Limited
2025-07-08 17:19:30,271 - INFO - app - app.py:220 - Successfully displayed monthly banking settlement for Kids Clinic India Limited
2025-07-08 17:19:30,467 - INFO - app - app.py:231 - Successfully displayed ToD generation analysis for Kids Clinic India Limited
2025-07-08 17:19:30,697 - INFO - app - app.py:242 - Successfully displayed ToD consumption analysis for Kids Clinic India Limited
2025-07-08 17:19:30,962 - INFO - app - app.py:253 - Successfully displayed hourly generation vs consumption chart for Kids Clinic India Limited
2025-07-08 17:19:31,452 - INFO - app - app.py:264 - Successfully displayed unit-wise analysis for Kids Clinic India Limited
2025-07-08 17:19:32,748 - INFO - app - app.py:275 - Successfully displayed unit-wise monthly bill analysis for Kids Clinic India Limited
2025-07-08 17:19:32,749 - INFO - app - app.py:287 - ToD Analysis tab: Displaying data for Kids Clinic India Limited
2025-07-08 17:20:35,466 - INFO - app - app.py:80 - Starting application initialization
2025-07-08 17:20:35,482 - INFO - app - app.py:82 - Page configuration completed
2025-07-08 17:20:35,482 - INFO - app - app.py:86 - Session state initialized
2025-07-08 17:20:35,482 - INFO - app - app.py:94 - User authenticated successfully
2025-07-08 17:20:35,483 - INFO - app - app.py:106 - Loading client data from database
2025-07-08 17:20:35,582 - INFO - app - app.py:115 - Successfully loaded data for 1 clients
2025-07-08 17:20:35,582 - INFO - app - app.py:63 - Successfully loaded image: logo/logo_integrum.jpg
2025-07-08 17:20:35,584 - INFO - app - app.py:146 - Client selection: Kids Clinic India Limited, Plant: None, Type: Combined
2025-07-08 17:20:35,584 - INFO - app - app.py:157 - Date range selected: 2025-05-01 to 2025-05-31
2025-07-08 17:20:35,590 - INFO - app - app.py:185 - Created main application tabs
2025-07-08 17:20:35,590 - INFO - app - app.py:195 - Summary tab: Displaying data for Kids Clinic India Limited
2025-07-08 17:20:36,502 - INFO - app - app.py:210 - Successfully displayed generation vs consumption chart for Kids Clinic India Limited
2025-07-08 17:20:37,500 - INFO - app - app.py:220 - Successfully displayed monthly banking settlement for Kids Clinic India Limited
2025-07-08 17:20:37,704 - INFO - app - app.py:231 - Successfully displayed ToD generation analysis for Kids Clinic India Limited
2025-07-08 17:20:37,915 - INFO - app - app.py:242 - Successfully displayed ToD consumption analysis for Kids Clinic India Limited
2025-07-08 17:20:38,137 - INFO - app - app.py:253 - Successfully displayed hourly generation vs consumption chart for Kids Clinic India Limited
2025-07-08 17:20:38,579 - INFO - app - app.py:264 - Successfully displayed unit-wise analysis for Kids Clinic India Limited
2025-07-08 17:20:39,766 - INFO - app - app.py:275 - Successfully displayed unit-wise monthly bill analysis for Kids Clinic India Limited
2025-07-08 17:20:39,766 - INFO - app - app.py:287 - ToD Analysis tab: Displaying data for Kids Clinic India Limited
2025-07-08 17:20:47,911 - INFO - app - app.py:80 - Starting application initialization
2025-07-08 17:20:47,912 - INFO - app - app.py:82 - Page configuration completed
2025-07-08 17:20:47,912 - INFO - app - app.py:86 - Session state initialized
2025-07-08 17:20:47,912 - INFO - app - app.py:94 - User authenticated successfully
2025-07-08 17:20:47,912 - INFO - app - app.py:106 - Loading client data from database
2025-07-08 17:20:48,037 - INFO - app - app.py:115 - Successfully loaded data for 1 clients
2025-07-08 17:20:48,037 - INFO - app - app.py:63 - Successfully loaded image: logo/logo_integrum.jpg
2025-07-08 17:20:48,037 - INFO - app - app.py:146 - Client selection: Kids Clinic India Limited, Plant: None, Type: Combined
2025-07-08 17:20:48,037 - INFO - app - app.py:157 - Date range selected: 2025-05-01 to 2025-05-31
2025-07-08 17:20:48,052 - INFO - app - app.py:185 - Created main application tabs
2025-07-08 17:20:48,054 - INFO - app - app.py:195 - Summary tab: Displaying data for Kids Clinic India Limited
2025-07-08 17:20:49,033 - INFO - app - app.py:210 - Successfully displayed generation vs consumption chart for Kids Clinic India Limited
2025-07-08 17:20:50,043 - INFO - app - app.py:220 - Successfully displayed monthly banking settlement for Kids Clinic India Limited
2025-07-08 17:20:50,350 - INFO - app - app.py:231 - Successfully displayed ToD generation analysis for Kids Clinic India Limited
2025-07-08 17:20:50,601 - INFO - app - app.py:242 - Successfully displayed ToD consumption analysis for Kids Clinic India Limited
2025-07-08 17:20:50,885 - INFO - app - app.py:253 - Successfully displayed hourly generation vs consumption chart for Kids Clinic India Limited
2025-07-08 17:20:51,368 - INFO - app - app.py:264 - Successfully displayed unit-wise analysis for Kids Clinic India Limited
2025-07-08 17:20:52,551 - INFO - app - app.py:275 - Successfully displayed unit-wise monthly bill analysis for Kids Clinic India Limited
2025-07-08 17:20:52,566 - INFO - app - app.py:287 - ToD Analysis tab: Displaying data for Kids Clinic India Limited
2025-07-08 17:21:15,021 - INFO - app - app.py:80 - Starting application initialization
2025-07-08 17:21:15,033 - INFO - app - app.py:82 - Page configuration completed
2025-07-08 17:21:15,033 - INFO - app - app.py:86 - Session state initialized
2025-07-08 17:21:15,034 - INFO - app - app.py:94 - User authenticated successfully
2025-07-08 17:21:15,035 - INFO - app - app.py:106 - Loading client data from database
2025-07-08 17:21:15,137 - INFO - app - app.py:115 - Successfully loaded data for 1 clients
2025-07-08 17:21:15,137 - INFO - app - app.py:63 - Successfully loaded image: logo/logo_integrum.jpg
2025-07-08 17:21:15,139 - INFO - app - app.py:146 - Client selection: Kids Clinic India Limited, Plant: None, Type: Combined
2025-07-08 17:21:15,140 - INFO - app - app.py:157 - Date range selected: 2025-05-01 to 2025-05-31
2025-07-08 17:21:15,144 - INFO - app - app.py:185 - Created main application tabs
2025-07-08 17:21:15,144 - INFO - app - app.py:195 - Summary tab: Displaying data for Kids Clinic India Limited
2025-07-08 17:21:15,990 - INFO - app - app.py:210 - Successfully displayed generation vs consumption chart for Kids Clinic India Limited
2025-07-08 17:21:17,005 - INFO - app - app.py:220 - Successfully displayed monthly banking settlement for Kids Clinic India Limited
2025-07-08 17:21:17,240 - INFO - app - app.py:231 - Successfully displayed ToD generation analysis for Kids Clinic India Limited
2025-07-08 17:21:17,505 - INFO - app - app.py:242 - Successfully displayed ToD consumption analysis for Kids Clinic India Limited
2025-07-08 17:21:17,860 - INFO - app - app.py:253 - Successfully displayed hourly generation vs consumption chart for Kids Clinic India Limited
2025-07-08 17:21:18,343 - INFO - app - app.py:264 - Successfully displayed unit-wise analysis for Kids Clinic India Limited
2025-07-08 17:21:19,547 - INFO - app - app.py:275 - Successfully displayed unit-wise monthly bill analysis for Kids Clinic India Limited
2025-07-08 17:21:19,547 - INFO - app - app.py:287 - ToD Analysis tab: Displaying data for Kids Clinic India Limited
2025-07-08 17:21:59,602 - INFO - app - app.py:80 - Starting application initialization
2025-07-08 17:21:59,603 - INFO - app - app.py:82 - Page configuration completed
2025-07-08 17:21:59,603 - INFO - app - app.py:86 - Session state initialized
2025-07-08 17:21:59,605 - INFO - app - app.py:94 - User authenticated successfully
2025-07-08 17:21:59,606 - INFO - app - app.py:106 - Loading client data from database
2025-07-08 17:21:59,715 - INFO - app - app.py:115 - Successfully loaded data for 1 clients
2025-07-08 17:21:59,715 - INFO - app - app.py:63 - Successfully loaded image: logo/logo_integrum.jpg
2025-07-08 17:21:59,717 - INFO - app - app.py:146 - Client selection: Kids Clinic India Limited, Plant: None, Type: Combined
2025-07-08 17:21:59,718 - INFO - app - app.py:157 - Date range selected: 2025-05-01 to 2025-05-31
2025-07-08 17:21:59,721 - INFO - app - app.py:185 - Created main application tabs
2025-07-08 17:21:59,721 - INFO - app - app.py:195 - Summary tab: Displaying data for Kids Clinic India Limited
2025-07-08 17:22:00,555 - INFO - app - app.py:210 - Successfully displayed generation vs consumption chart for Kids Clinic India Limited
2025-07-08 17:22:01,560 - INFO - app - app.py:220 - Successfully displayed monthly banking settlement for Kids Clinic India Limited
2025-07-08 17:22:01,876 - INFO - app - app.py:231 - Successfully displayed ToD generation analysis for Kids Clinic India Limited
2025-07-08 17:22:02,095 - INFO - app - app.py:242 - Successfully displayed ToD consumption analysis for Kids Clinic India Limited
2025-07-08 17:22:02,362 - INFO - app - app.py:253 - Successfully displayed hourly generation vs consumption chart for Kids Clinic India Limited
2025-07-08 17:22:02,856 - INFO - app - app.py:264 - Successfully displayed unit-wise analysis for Kids Clinic India Limited
2025-07-08 17:22:04,215 - INFO - app - app.py:275 - Successfully displayed unit-wise monthly bill analysis for Kids Clinic India Limited
2025-07-08 17:22:04,215 - INFO - app - app.py:287 - ToD Analysis tab: Displaying data for Kids Clinic India Limited
2025-07-08 17:22:38,367 - INFO - app - app.py:80 - Starting application initialization
2025-07-08 17:22:38,394 - INFO - app - app.py:82 - Page configuration completed
2025-07-08 17:22:38,394 - INFO - app - app.py:86 - Session state initialized
2025-07-08 17:22:38,394 - INFO - app - app.py:94 - User authenticated successfully
2025-07-08 17:22:38,398 - INFO - app - app.py:106 - Loading client data from database
2025-07-08 17:22:38,508 - INFO - app - app.py:115 - Successfully loaded data for 1 clients
2025-07-08 17:22:38,508 - INFO - app - app.py:63 - Successfully loaded image: logo/logo_integrum.jpg
2025-07-08 17:22:38,511 - INFO - app - app.py:146 - Client selection: Kids Clinic India Limited, Plant: None, Type: Combined
2025-07-08 17:22:38,512 - INFO - app - app.py:157 - Date range selected: 2025-05-01 to 2025-05-31
2025-07-08 17:22:38,515 - INFO - app - app.py:185 - Created main application tabs
2025-07-08 17:22:38,515 - INFO - app - app.py:195 - Summary tab: Displaying data for Kids Clinic India Limited
2025-07-08 17:22:39,362 - INFO - app - app.py:210 - Successfully displayed generation vs consumption chart for Kids Clinic India Limited
2025-07-08 17:22:40,472 - INFO - app - app.py:220 - Successfully displayed monthly banking settlement for Kids Clinic India Limited
2025-07-08 17:22:40,692 - INFO - app - app.py:231 - Successfully displayed ToD generation analysis for Kids Clinic India Limited
2025-07-08 17:22:40,894 - INFO - app - app.py:242 - Successfully displayed ToD consumption analysis for Kids Clinic India Limited
2025-07-08 17:22:41,111 - INFO - app - app.py:253 - Successfully displayed hourly generation vs consumption chart for Kids Clinic India Limited
2025-07-08 17:22:42,364 - INFO - app - app.py:275 - Successfully displayed unit-wise monthly bill analysis for Kids Clinic India Limited
2025-07-08 17:22:42,365 - INFO - app - app.py:287 - ToD Analysis tab: Displaying data for Kids Clinic India Limited
2025-07-08 17:22:59,644 - INFO - app - app.py:80 - Starting application initialization
2025-07-08 17:22:59,644 - INFO - app - app.py:82 - Page configuration completed
2025-07-08 17:22:59,656 - INFO - app - app.py:86 - Session state initialized
2025-07-08 17:22:59,656 - INFO - app - app.py:94 - User authenticated successfully
2025-07-08 17:22:59,657 - INFO - app - app.py:106 - Loading client data from database
2025-07-08 17:22:59,801 - INFO - app - app.py:115 - Successfully loaded data for 1 clients
2025-07-08 17:22:59,801 - INFO - app - app.py:63 - Successfully loaded image: logo/logo_integrum.jpg
2025-07-08 17:22:59,804 - INFO - app - app.py:146 - Client selection: Kids Clinic India Limited, Plant: None, Type: Combined
2025-07-08 17:22:59,806 - INFO - app - app.py:157 - Date range selected: 2025-05-01 to 2025-05-31
2025-07-08 17:22:59,810 - INFO - app - app.py:185 - Created main application tabs
2025-07-08 17:22:59,811 - INFO - app - app.py:195 - Summary tab: Displaying data for Kids Clinic India Limited
2025-07-08 17:23:00,682 - INFO - app - app.py:210 - Successfully displayed generation vs consumption chart for Kids Clinic India Limited
2025-07-08 17:23:01,669 - INFO - app - app.py:220 - Successfully displayed monthly banking settlement for Kids Clinic India Limited
2025-07-08 17:23:01,950 - INFO - app - app.py:231 - Successfully displayed ToD generation analysis for Kids Clinic India Limited
2025-07-08 17:23:02,195 - INFO - app - app.py:242 - Successfully displayed ToD consumption analysis for Kids Clinic India Limited
2025-07-08 17:23:02,468 - INFO - app - app.py:253 - Successfully displayed hourly generation vs consumption chart for Kids Clinic India Limited
2025-07-08 17:23:03,712 - INFO - app - app.py:275 - Successfully displayed unit-wise monthly bill analysis for Kids Clinic India Limited
2025-07-08 17:23:03,727 - INFO - app - app.py:287 - ToD Analysis tab: Displaying data for Kids Clinic India Limited
2025-07-08 17:28:10,862 - INFO - app - app.py:80 - Starting application initialization
2025-07-08 17:28:10,862 - INFO - app - app.py:82 - Page configuration completed
2025-07-08 17:28:10,864 - INFO - app - app.py:86 - Session state initialized
2025-07-08 17:28:10,864 - INFO - app - app.py:94 - User authenticated successfully
2025-07-08 17:28:10,865 - INFO - app - app.py:106 - Loading client data from database
2025-07-08 17:28:10,968 - INFO - app - app.py:115 - Successfully loaded data for 1 clients
2025-07-08 17:28:10,968 - INFO - app - app.py:63 - Successfully loaded image: logo/logo_integrum.jpg
2025-07-08 17:28:10,968 - INFO - app - app.py:146 - Client selection: Kids Clinic India Limited, Plant: None, Type: Combined
2025-07-08 17:28:10,968 - INFO - app - app.py:157 - Date range selected: 2025-05-01 to 2025-05-31
2025-07-08 17:28:10,968 - INFO - app - app.py:185 - Created main application tabs
2025-07-08 17:28:10,968 - INFO - app - app.py:195 - Summary tab: Displaying data for Kids Clinic India Limited
2025-07-08 17:28:11,949 - INFO - app - app.py:210 - Successfully displayed generation vs consumption chart for Kids Clinic India Limited
2025-07-08 17:28:12,981 - INFO - app - app.py:220 - Successfully displayed monthly banking settlement for Kids Clinic India Limited
2025-07-08 17:28:13,264 - INFO - app - app.py:231 - Successfully displayed ToD generation analysis for Kids Clinic India Limited
2025-07-08 17:28:13,473 - INFO - app - app.py:242 - Successfully displayed ToD consumption analysis for Kids Clinic India Limited
2025-07-08 17:28:13,723 - INFO - app - app.py:253 - Successfully displayed hourly generation vs consumption chart for Kids Clinic India Limited
2025-07-08 17:28:14,967 - INFO - app - app.py:275 - Successfully displayed unit-wise monthly bill analysis for Kids Clinic India Limited
2025-07-08 17:28:14,967 - INFO - app - app.py:287 - ToD Analysis tab: Displaying data for Kids Clinic India Limited
2025-07-08 17:28:15,469 - INFO - app - app.py:304 - Successfully displayed ToD generation vs consumption for Kids Clinic India Limited
2025-07-08 17:29:56,213 - INFO - app - app.py:80 - Starting application initialization
2025-07-08 17:29:56,213 - INFO - app - app.py:82 - Page configuration completed
2025-07-08 17:29:56,213 - INFO - app - app.py:86 - Session state initialized
2025-07-08 17:29:56,213 - INFO - app - app.py:94 - User authenticated successfully
2025-07-08 17:29:56,213 - INFO - app - app.py:106 - Loading client data from database
2025-07-08 17:29:56,340 - INFO - app - app.py:115 - Successfully loaded data for 1 clients
2025-07-08 17:29:56,340 - INFO - app - app.py:63 - Successfully loaded image: logo/logo_integrum.jpg
2025-07-08 17:29:56,356 - INFO - app - app.py:146 - Client selection: Kids Clinic India Limited, Plant: None, Type: Combined
2025-07-08 17:29:56,356 - INFO - app - app.py:157 - Date range selected: 2025-05-01 to 2025-05-31
2025-07-08 17:29:56,361 - INFO - app - app.py:185 - Created main application tabs
2025-07-08 17:29:56,363 - INFO - app - app.py:195 - Summary tab: Displaying data for Kids Clinic India Limited
2025-07-08 17:29:57,244 - INFO - app - app.py:210 - Successfully displayed generation vs consumption chart for Kids Clinic India Limited
2025-07-08 17:29:58,182 - INFO - app - app.py:220 - Successfully displayed monthly banking settlement for Kids Clinic India Limited
2025-07-08 17:29:58,563 - INFO - app - app.py:232 - Successfully displayed monthly ToD before banking for Kids Clinic India Limited
2025-07-08 17:29:58,826 - INFO - app - app.py:243 - Successfully displayed hourly generation vs consumption chart for Kids Clinic India Limited
2025-07-08 17:30:00,102 - INFO - app - app.py:265 - Successfully displayed unit-wise monthly bill analysis for Kids Clinic India Limited
2025-07-08 17:30:00,102 - INFO - app - app.py:277 - ToD Analysis tab: Displaying data for Kids Clinic India Limited
2025-07-08 17:30:00,551 - INFO - app - app.py:294 - Successfully displayed ToD generation vs consumption for Kids Clinic India Limited
2025-07-08 17:30:57,384 - INFO - app - app.py:80 - Starting application initialization
2025-07-08 17:30:57,386 - INFO - app - app.py:82 - Page configuration completed
2025-07-08 17:30:57,386 - INFO - app - app.py:86 - Session state initialized
2025-07-08 17:30:57,387 - INFO - app - app.py:94 - User authenticated successfully
2025-07-08 17:30:57,387 - INFO - app - app.py:106 - Loading client data from database
2025-07-08 17:30:57,497 - INFO - app - app.py:115 - Successfully loaded data for 1 clients
2025-07-08 17:30:57,497 - INFO - app - app.py:63 - Successfully loaded image: logo/logo_integrum.jpg
2025-07-08 17:30:57,497 - INFO - app - app.py:146 - Client selection: Kids Clinic India Limited, Plant: None, Type: Combined
2025-07-08 17:30:57,497 - INFO - app - app.py:157 - Date range selected: 2025-05-01 to 2025-05-31
2025-07-08 17:30:57,497 - INFO - app - app.py:185 - Created main application tabs
2025-07-08 17:30:57,497 - INFO - app - app.py:195 - Summary tab: Displaying data for Kids Clinic India Limited
2025-07-08 17:30:58,366 - INFO - app - app.py:210 - Successfully displayed generation vs consumption chart for Kids Clinic India Limited
2025-07-08 17:30:59,426 - INFO - app - app.py:220 - Successfully displayed monthly banking settlement for Kids Clinic India Limited
2025-07-08 17:30:59,629 - INFO - app - app.py:231 - Successfully displayed ToD generation analysis for Kids Clinic India Limited
2025-07-08 17:30:59,839 - INFO - app - app.py:242 - Successfully displayed ToD consumption analysis for Kids Clinic India Limited
2025-07-08 17:31:00,103 - INFO - app - app.py:253 - Successfully displayed hourly generation vs consumption chart for Kids Clinic India Limited
2025-07-08 17:31:01,390 - INFO - app - app.py:275 - Successfully displayed unit-wise monthly bill analysis for Kids Clinic India Limited
2025-07-08 17:31:01,390 - INFO - app - app.py:287 - ToD Analysis tab: Displaying data for Kids Clinic India Limited
2025-07-08 17:31:01,702 - INFO - app - app.py:300 - Successfully displayed monthly ToD before banking for Kids Clinic India Limited
2025-07-08 17:31:02,146 - INFO - app - app.py:310 - Successfully displayed ToD generation vs consumption for Kids Clinic India Limited
2025-07-08 17:40:59,207 - INFO - app - app.py:80 - Starting application initialization
2025-07-08 17:40:59,208 - INFO - app - app.py:82 - Page configuration completed
2025-07-08 17:40:59,210 - INFO - app - app.py:86 - Session state initialized
2025-07-08 17:40:59,211 - INFO - app - app.py:94 - User authenticated successfully
2025-07-08 17:40:59,213 - INFO - app - app.py:106 - Loading client data from database
2025-07-08 17:40:59,356 - INFO - app - app.py:115 - Successfully loaded data for 1 clients
2025-07-08 17:40:59,357 - INFO - app - app.py:63 - Successfully loaded image: logo/logo_integrum.jpg
2025-07-08 17:40:59,359 - INFO - app - app.py:146 - Client selection: Kids Clinic India Limited, Plant: None, Type: Combined
2025-07-08 17:40:59,362 - INFO - app - app.py:157 - Date range selected: 2025-05-01 to 2025-05-31
2025-07-08 17:40:59,367 - INFO - app - app.py:185 - Created main application tabs
2025-07-08 17:40:59,367 - INFO - app - app.py:195 - Summary tab: Displaying data for Kids Clinic India Limited
2025-07-08 17:41:00,688 - INFO - app - app.py:210 - Successfully displayed generation vs consumption chart for Kids Clinic India Limited
2025-07-08 17:41:02,696 - INFO - app - app.py:220 - Successfully displayed monthly banking settlement for Kids Clinic India Limited
2025-07-08 17:41:02,975 - INFO - app - app.py:231 - Successfully displayed ToD generation analysis for Kids Clinic India Limited
2025-07-08 17:41:03,237 - INFO - app - app.py:242 - Successfully displayed ToD consumption analysis for Kids Clinic India Limited
2025-07-08 17:41:03,542 - INFO - app - app.py:253 - Successfully displayed hourly generation vs consumption chart for Kids Clinic India Limited
2025-07-08 17:41:05,849 - INFO - app - app.py:275 - Successfully displayed unit-wise monthly bill analysis for Kids Clinic India Limited
2025-07-08 17:41:05,857 - INFO - app - app.py:287 - ToD Analysis tab: Displaying data for Kids Clinic India Limited
2025-07-08 17:41:06,307 - INFO - app - app.py:300 - Successfully displayed monthly ToD before banking for Kids Clinic India Limited
2025-07-08 17:41:06,976 - INFO - app - app.py:310 - Successfully displayed ToD generation vs consumption for Kids Clinic India Limited
2025-07-08 17:41:14,923 - INFO - app - app.py:80 - Starting application initialization
2025-07-08 17:41:14,924 - INFO - app - app.py:82 - Page configuration completed
2025-07-08 17:41:14,925 - INFO - app - app.py:86 - Session state initialized
2025-07-08 17:41:14,926 - INFO - app - app.py:94 - User authenticated successfully
2025-07-08 17:41:14,927 - INFO - app - app.py:106 - Loading client data from database
2025-07-08 17:41:15,058 - INFO - app - app.py:115 - Successfully loaded data for 1 clients
2025-07-08 17:41:15,058 - INFO - app - app.py:63 - Successfully loaded image: logo/logo_integrum.jpg
2025-07-08 17:41:15,062 - INFO - app - app.py:146 - Client selection: Kids Clinic India Limited, Plant: None, Type: Combined
2025-07-08 17:41:15,063 - INFO - app - app.py:157 - Date range selected: 2025-05-01 to 2025-05-31
2025-07-08 17:41:15,070 - INFO - app - app.py:185 - Created main application tabs
2025-07-08 17:41:15,070 - INFO - app - app.py:195 - Summary tab: Displaying data for Kids Clinic India Limited
2025-07-08 17:41:16,297 - INFO - app - app.py:210 - Successfully displayed generation vs consumption chart for Kids Clinic India Limited
2025-07-08 17:41:17,992 - INFO - app - app.py:220 - Successfully displayed monthly banking settlement for Kids Clinic India Limited
2025-07-08 17:41:18,289 - INFO - app - app.py:231 - Successfully displayed ToD generation analysis for Kids Clinic India Limited
2025-07-08 17:41:18,546 - INFO - app - app.py:242 - Successfully displayed ToD consumption analysis for Kids Clinic India Limited
2025-07-08 17:41:18,837 - INFO - app - app.py:253 - Successfully displayed hourly generation vs consumption chart for Kids Clinic India Limited
2025-07-08 17:41:21,180 - INFO - app - app.py:275 - Successfully displayed unit-wise monthly bill analysis for Kids Clinic India Limited
2025-07-08 17:41:21,180 - INFO - app - app.py:287 - ToD Analysis tab: Displaying data for Kids Clinic India Limited
2025-07-08 17:41:21,534 - INFO - app - app.py:300 - Successfully displayed monthly ToD before banking for Kids Clinic India Limited
2025-07-08 17:41:22,167 - INFO - app - app.py:310 - Successfully displayed ToD generation vs consumption for Kids Clinic India Limited
2025-07-08 17:41:55,483 - INFO - app - app.py:80 - Starting application initialization
2025-07-08 17:41:55,484 - INFO - app - app.py:82 - Page configuration completed
2025-07-08 17:41:55,484 - INFO - app - app.py:86 - Session state initialized
2025-07-08 17:41:55,486 - INFO - app - app.py:94 - User authenticated successfully
2025-07-08 17:41:55,486 - INFO - app - app.py:106 - Loading client data from database
2025-07-08 17:41:55,596 - INFO - app - app.py:115 - Successfully loaded data for 1 clients
2025-07-08 17:41:55,596 - INFO - app - app.py:63 - Successfully loaded image: logo/logo_integrum.jpg
2025-07-08 17:41:55,596 - INFO - app - app.py:146 - Client selection: Kids Clinic India Limited, Plant: None, Type: Combined
2025-07-08 17:41:55,596 - INFO - app - app.py:157 - Date range selected: 2025-05-01 to 2025-05-31
2025-07-08 17:41:55,610 - INFO - app - app.py:185 - Created main application tabs
2025-07-08 17:41:55,614 - INFO - app - app.py:195 - Summary tab: Displaying data for Kids Clinic India Limited
2025-07-08 17:41:56,596 - INFO - app - app.py:210 - Successfully displayed generation vs consumption chart for Kids Clinic India Limited
2025-07-08 17:41:57,559 - INFO - app - app.py:220 - Successfully displayed monthly banking settlement for Kids Clinic India Limited
2025-07-08 17:41:57,842 - INFO - app - app.py:231 - Successfully displayed ToD generation analysis for Kids Clinic India Limited
2025-07-08 17:41:58,145 - INFO - app - app.py:242 - Successfully displayed ToD consumption analysis for Kids Clinic India Limited
2025-07-08 17:41:58,463 - INFO - app - app.py:253 - Successfully displayed hourly generation vs consumption chart for Kids Clinic India Limited
2025-07-08 17:41:59,808 - INFO - app - app.py:275 - Successfully displayed unit-wise monthly bill analysis for Kids Clinic India Limited
2025-07-08 17:41:59,808 - INFO - app - app.py:287 - ToD Analysis tab: Displaying data for Kids Clinic India Limited
2025-07-08 17:42:00,219 - INFO - app - app.py:300 - Successfully displayed monthly ToD before banking for Kids Clinic India Limited
2025-07-08 17:42:00,773 - INFO - app - app.py:310 - Successfully displayed ToD generation vs consumption for Kids Clinic India Limited
2025-07-08 17:42:16,665 - INFO - app - app.py:81 - Starting application initialization
2025-07-08 17:42:16,669 - INFO - app - app.py:83 - Page configuration completed
2025-07-08 17:42:16,669 - INFO - app - app.py:87 - Session state initialized
2025-07-08 17:42:16,669 - INFO - app - app.py:95 - User authenticated successfully
2025-07-08 17:42:16,671 - INFO - app - app.py:107 - Loading client data from database
2025-07-08 17:42:16,884 - INFO - app - app.py:116 - Successfully loaded data for 1 clients
2025-07-08 17:42:16,884 - INFO - app - app.py:64 - Successfully loaded image: logo/logo_integrum.jpg
2025-07-08 17:42:16,887 - INFO - app - app.py:147 - Client selection: Kids Clinic India Limited, Plant: None, Type: Combined
2025-07-08 17:42:16,890 - INFO - app - app.py:158 - Date range selected: 2025-05-01 to 2025-05-31
2025-07-08 17:42:16,893 - INFO - app - app.py:186 - Created main application tabs
2025-07-08 17:42:16,893 - INFO - app - app.py:196 - Summary tab: Displaying data for Kids Clinic India Limited
2025-07-08 17:42:17,827 - INFO - app - app.py:211 - Successfully displayed generation vs consumption chart for Kids Clinic India Limited
2025-07-08 17:42:18,789 - INFO - app - app.py:221 - Successfully displayed monthly banking settlement for Kids Clinic India Limited
2025-07-08 17:42:19,022 - INFO - app - app.py:232 - Successfully displayed ToD generation analysis for Kids Clinic India Limited
2025-07-08 17:42:19,290 - INFO - app - app.py:243 - Successfully displayed ToD consumption analysis for Kids Clinic India Limited
2025-07-08 17:42:19,652 - INFO - app - app.py:254 - Successfully displayed hourly generation vs consumption chart for Kids Clinic India Limited
2025-07-08 17:42:21,949 - INFO - app - app.py:276 - Successfully displayed unit-wise monthly bill analysis for Kids Clinic India Limited
2025-07-08 17:42:21,962 - INFO - app - app.py:288 - ToD Analysis tab: Displaying data for Kids Clinic India Limited
2025-07-08 17:42:22,394 - INFO - app - app.py:301 - Successfully displayed monthly ToD before banking for Kids Clinic India Limited
2025-07-08 17:42:23,116 - INFO - app - app.py:311 - Successfully displayed ToD generation vs consumption for Kids Clinic India Limited
2025-07-08 17:42:33,474 - INFO - app - app.py:81 - Starting application initialization
2025-07-08 17:42:33,475 - INFO - app - app.py:83 - Page configuration completed
2025-07-08 17:42:33,477 - INFO - app - app.py:87 - Session state initialized
2025-07-08 17:42:33,477 - INFO - app - app.py:95 - User authenticated successfully
2025-07-08 17:42:33,478 - INFO - app - app.py:107 - Loading client data from database
2025-07-08 17:42:33,569 - INFO - app - app.py:116 - Successfully loaded data for 1 clients
2025-07-08 17:42:33,569 - INFO - app - app.py:64 - Successfully loaded image: logo/logo_integrum.jpg
2025-07-08 17:42:33,570 - INFO - app - app.py:147 - Client selection: Kids Clinic India Limited, Plant: None, Type: Combined
2025-07-08 17:42:33,572 - INFO - app - app.py:158 - Date range selected: 2025-05-01 to 2025-05-31
2025-07-08 17:42:33,575 - INFO - app - app.py:186 - Created main application tabs
2025-07-08 17:42:33,575 - INFO - app - app.py:196 - Summary tab: Displaying data for Kids Clinic India Limited
2025-07-08 17:42:34,458 - INFO - app - app.py:211 - Successfully displayed generation vs consumption chart for Kids Clinic India Limited
2025-07-08 17:42:35,436 - INFO - app - app.py:221 - Successfully displayed monthly banking settlement for Kids Clinic India Limited
2025-07-08 17:42:35,692 - INFO - app - app.py:232 - Successfully displayed ToD generation analysis for Kids Clinic India Limited
2025-07-08 17:42:35,901 - INFO - app - app.py:243 - Successfully displayed ToD consumption analysis for Kids Clinic India Limited
2025-07-08 17:42:36,150 - INFO - app - app.py:254 - Successfully displayed hourly generation vs consumption chart for Kids Clinic India Limited
2025-07-08 17:42:37,347 - INFO - app - app.py:276 - Successfully displayed unit-wise monthly bill analysis for Kids Clinic India Limited
2025-07-08 17:42:37,348 - INFO - app - app.py:288 - ToD Analysis tab: Displaying data for Kids Clinic India Limited
2025-07-08 17:42:37,679 - INFO - app - app.py:301 - Successfully displayed monthly ToD before banking for Kids Clinic India Limited
2025-07-08 17:42:38,169 - INFO - app - app.py:311 - Successfully displayed ToD generation vs consumption for Kids Clinic India Limited
2025-07-08 17:42:38,566 - INFO - app - app.py:322 - Successfully displayed ToD line chart for Kids Clinic India Limited
2025-07-08 17:45:07,977 - INFO - app - app.py:81 - Starting application initialization
2025-07-08 17:45:07,978 - INFO - app - app.py:83 - Page configuration completed
2025-07-08 17:45:07,979 - INFO - app - app.py:87 - Session state initialized
2025-07-08 17:45:07,979 - INFO - app - app.py:95 - User authenticated successfully
2025-07-08 17:45:07,980 - INFO - app - app.py:107 - Loading client data from database
2025-07-08 17:45:08,070 - INFO - app - app.py:116 - Successfully loaded data for 1 clients
2025-07-08 17:45:08,071 - INFO - app - app.py:64 - Successfully loaded image: logo/logo_integrum.jpg
2025-07-08 17:45:08,074 - INFO - app - app.py:147 - Client selection: None, Plant: None, Type: None
2025-07-08 17:45:08,077 - INFO - app - app.py:158 - Date range selected: 2025-07-08 to 2025-07-08
2025-07-08 17:45:08,081 - INFO - app - app.py:351 - No client selected, showing welcome message
2025-07-08 17:45:12,815 - INFO - app - app.py:81 - Starting application initialization
2025-07-08 17:45:12,815 - INFO - app - app.py:83 - Page configuration completed
2025-07-08 17:45:12,815 - INFO - app - app.py:87 - Session state initialized
2025-07-08 17:45:12,815 - INFO - app - app.py:95 - User authenticated successfully
2025-07-08 17:45:12,815 - INFO - app - app.py:107 - Loading client data from database
2025-07-08 17:45:12,910 - INFO - app - app.py:116 - Successfully loaded data for 1 clients
2025-07-08 17:45:12,910 - INFO - app - app.py:64 - Successfully loaded image: logo/logo_integrum.jpg
2025-07-08 17:45:12,910 - INFO - app - app.py:147 - Client selection: Kids Clinic India Limited, Plant: None, Type: Combined
2025-07-08 17:45:12,910 - INFO - app - app.py:158 - Date range selected: 2025-07-08 to 2025-07-08
2025-07-08 17:45:12,920 - INFO - app - app.py:186 - Created main application tabs
2025-07-08 17:45:12,920 - INFO - app - app.py:196 - Summary tab: Displaying data for Kids Clinic India Limited
2025-07-08 17:45:13,677 - INFO - app - app.py:211 - Successfully displayed generation vs consumption chart for Kids Clinic India Limited
2025-07-08 17:45:14,602 - INFO - app - app.py:221 - Successfully displayed monthly banking settlement for Kids Clinic India Limited
2025-07-08 17:45:14,665 - INFO - app - app.py:232 - Successfully displayed ToD generation analysis for Kids Clinic India Limited
2025-07-08 17:45:14,748 - INFO - app - app.py:243 - Successfully displayed ToD consumption analysis for Kids Clinic India Limited
2025-07-08 17:45:14,876 - INFO - app - app.py:254 - Successfully displayed hourly generation vs consumption chart for Kids Clinic India Limited
2025-07-08 17:45:16,161 - INFO - app - app.py:276 - Successfully displayed unit-wise monthly bill analysis for Kids Clinic India Limited
2025-07-08 17:45:16,161 - INFO - app - app.py:288 - ToD Analysis tab: Displaying data for Kids Clinic India Limited
2025-07-08 17:45:16,549 - INFO - app - app.py:301 - Successfully displayed monthly ToD before banking for Kids Clinic India Limited
2025-07-08 17:45:16,627 - INFO - app - app.py:311 - Successfully displayed ToD generation vs consumption for Kids Clinic India Limited
2025-07-08 17:45:16,737 - INFO - app - app.py:322 - Successfully displayed ToD line chart for Kids Clinic India Limited
2025-07-08 17:45:34,636 - INFO - app - app.py:81 - Starting application initialization
2025-07-08 17:45:34,637 - INFO - app - app.py:83 - Page configuration completed
2025-07-08 17:45:34,637 - INFO - app - app.py:87 - Session state initialized
2025-07-08 17:45:34,637 - INFO - app - app.py:95 - User authenticated successfully
2025-07-08 17:45:34,637 - INFO - app - app.py:107 - Loading client data from database
2025-07-08 17:45:34,715 - INFO - app - app.py:116 - Successfully loaded data for 1 clients
2025-07-08 17:45:34,716 - INFO - app - app.py:64 - Successfully loaded image: logo/logo_integrum.jpg
2025-07-08 17:45:34,717 - INFO - app - app.py:147 - Client selection: Kids Clinic India Limited, Plant: None, Type: Combined
2025-07-08 17:45:34,720 - INFO - app - app.py:158 - Date range selected: 2025-05-01 to 2025-05-01
2025-07-08 17:45:34,723 - INFO - app - app.py:186 - Created main application tabs
2025-07-08 17:45:34,723 - INFO - app - app.py:196 - Summary tab: Displaying data for Kids Clinic India Limited
2025-07-08 17:45:35,587 - INFO - app - app.py:211 - Successfully displayed generation vs consumption chart for Kids Clinic India Limited
2025-07-08 17:45:35,666 - INFO - app - app.py:81 - Starting application initialization
2025-07-08 17:45:35,667 - INFO - app - app.py:83 - Page configuration completed
2025-07-08 17:45:35,668 - INFO - app - app.py:87 - Session state initialized
2025-07-08 17:45:35,668 - INFO - app - app.py:95 - User authenticated successfully
2025-07-08 17:45:35,669 - INFO - app - app.py:107 - Loading client data from database
2025-07-08 17:45:35,782 - INFO - app - app.py:116 - Successfully loaded data for 1 clients
2025-07-08 17:45:35,783 - INFO - app - app.py:64 - Successfully loaded image: logo/logo_integrum.jpg
2025-07-08 17:45:35,784 - INFO - app - app.py:147 - Client selection: Kids Clinic India Limited, Plant: None, Type: Combined
2025-07-08 17:45:35,786 - INFO - app - app.py:158 - Date range selected: 2025-05-01 to 2025-05-31
2025-07-08 17:45:35,790 - INFO - app - app.py:186 - Created main application tabs
2025-07-08 17:45:35,791 - INFO - app - app.py:196 - Summary tab: Displaying data for Kids Clinic India Limited
2025-07-08 17:45:36,907 - INFO - app - app.py:211 - Successfully displayed generation vs consumption chart for Kids Clinic India Limited
2025-07-08 17:45:37,889 - INFO - app - app.py:221 - Successfully displayed monthly banking settlement for Kids Clinic India Limited
2025-07-08 17:45:38,104 - INFO - app - app.py:232 - Successfully displayed ToD generation analysis for Kids Clinic India Limited
2025-07-08 17:45:38,316 - INFO - app - app.py:243 - Successfully displayed ToD consumption analysis for Kids Clinic India Limited
2025-07-08 17:45:38,586 - INFO - app - app.py:254 - Successfully displayed hourly generation vs consumption chart for Kids Clinic India Limited
2025-07-08 17:45:39,877 - INFO - app - app.py:276 - Successfully displayed unit-wise monthly bill analysis for Kids Clinic India Limited
2025-07-08 17:45:39,878 - INFO - app - app.py:288 - ToD Analysis tab: Displaying data for Kids Clinic India Limited
2025-07-08 17:45:40,195 - INFO - app - app.py:301 - Successfully displayed monthly ToD before banking for Kids Clinic India Limited
2025-07-08 17:45:40,640 - INFO - app - app.py:311 - Successfully displayed ToD generation vs consumption for Kids Clinic India Limited
2025-07-08 17:45:40,876 - INFO - app - app.py:322 - Successfully displayed ToD line chart for Kids Clinic India Limited
2025-07-08 17:46:55,646 - INFO - app - app.py:81 - Starting application initialization
2025-07-08 17:46:55,649 - INFO - app - app.py:83 - Page configuration completed
2025-07-08 17:46:55,650 - INFO - app - app.py:87 - Session state initialized
2025-07-08 17:46:55,650 - INFO - app - app.py:95 - User authenticated successfully
2025-07-08 17:46:55,651 - INFO - app - app.py:107 - Loading client data from database
2025-07-08 17:46:55,772 - INFO - app - app.py:116 - Successfully loaded data for 1 clients
2025-07-08 17:46:55,772 - INFO - app - app.py:64 - Successfully loaded image: logo/logo_integrum.jpg
2025-07-08 17:46:55,774 - INFO - app - app.py:147 - Client selection: Kids Clinic India Limited, Plant: None, Type: Combined
2025-07-08 17:46:55,775 - INFO - app - app.py:158 - Date range selected: 2025-05-01 to 2025-05-31
2025-07-08 17:46:55,778 - INFO - app - app.py:186 - Created main application tabs
2025-07-08 17:46:55,779 - INFO - app - app.py:196 - Summary tab: Displaying data for Kids Clinic India Limited
2025-07-08 17:46:56,667 - INFO - app - app.py:211 - Successfully displayed generation vs consumption chart for Kids Clinic India Limited
2025-07-08 17:46:57,705 - INFO - app - app.py:221 - Successfully displayed monthly banking settlement for Kids Clinic India Limited
2025-07-08 17:46:57,918 - INFO - app - app.py:232 - Successfully displayed ToD generation analysis for Kids Clinic India Limited
2025-07-08 17:46:58,150 - INFO - app - app.py:243 - Successfully displayed ToD consumption analysis for Kids Clinic India Limited
2025-07-08 17:46:58,458 - INFO - app - app.py:254 - Successfully displayed hourly generation vs consumption chart for Kids Clinic India Limited
2025-07-08 17:46:59,598 - INFO - app - app.py:276 - Successfully displayed unit-wise monthly bill analysis for Kids Clinic India Limited
2025-07-08 17:46:59,599 - INFO - app - app.py:288 - ToD Analysis tab: Displaying data for Kids Clinic India Limited
2025-07-08 17:46:59,978 - INFO - app - app.py:301 - Successfully displayed monthly ToD before banking for Kids Clinic India Limited
2025-07-08 17:47:00,418 - INFO - app - app.py:311 - Successfully displayed ToD generation vs consumption for Kids Clinic India Limited
2025-07-08 17:47:00,813 - INFO - app - app.py:322 - Successfully displayed ToD line chart for Kids Clinic India Limited
2025-07-08 18:02:22,980 - INFO - app - app.py:81 - Starting application initialization
2025-07-08 18:02:22,982 - INFO - app - app.py:83 - Page configuration completed
2025-07-08 18:02:22,985 - INFO - app - app.py:87 - Session state initialized
2025-07-08 18:02:22,986 - INFO - app - app.py:95 - User authenticated successfully
2025-07-08 18:02:22,988 - INFO - app - app.py:107 - Loading client data from database
2025-07-08 18:02:23,118 - INFO - app - app.py:116 - Successfully loaded data for 1 clients
2025-07-08 18:02:23,119 - INFO - app - app.py:64 - Successfully loaded image: logo/logo_integrum.jpg
2025-07-08 18:02:23,121 - INFO - app - app.py:147 - Client selection: Kids Clinic India Limited, Plant: None, Type: Combined
2025-07-08 18:02:23,122 - INFO - app - app.py:158 - Date range selected: 2025-05-01 to 2025-05-31
2025-07-08 18:02:23,127 - INFO - app - app.py:186 - Created main application tabs
2025-07-08 18:02:23,127 - INFO - app - app.py:196 - Summary tab: Displaying data for Kids Clinic India Limited
2025-07-08 18:02:24,153 - INFO - app - app.py:211 - Successfully displayed generation vs consumption chart for Kids Clinic India Limited
2025-07-08 18:02:25,161 - INFO - app - app.py:221 - Successfully displayed monthly banking settlement for Kids Clinic India Limited
2025-07-08 18:02:25,409 - INFO - app - app.py:232 - Successfully displayed ToD generation analysis for Kids Clinic India Limited
2025-07-08 18:02:25,676 - INFO - app - app.py:243 - Successfully displayed ToD consumption analysis for Kids Clinic India Limited
2025-07-08 18:02:25,959 - INFO - app - app.py:254 - Successfully displayed hourly generation vs consumption chart for Kids Clinic India Limited
2025-07-08 18:02:27,295 - INFO - app - app.py:276 - Successfully displayed unit-wise monthly bill analysis for Kids Clinic India Limited
2025-07-08 18:02:27,296 - INFO - app - app.py:288 - ToD Analysis tab: Displaying data for Kids Clinic India Limited
2025-07-08 18:02:27,621 - INFO - app - app.py:301 - Successfully displayed monthly ToD before banking for Kids Clinic India Limited
2025-07-08 18:02:28,116 - INFO - app - app.py:311 - Successfully displayed ToD generation vs consumption for Kids Clinic India Limited
2025-07-08 18:02:28,355 - INFO - app - app.py:322 - Successfully displayed ToD line chart for Kids Clinic India Limited
2025-07-09 10:32:48,469 - INFO - app - app.py:82 - Starting application initialization
2025-07-09 10:32:48,508 - INFO - app - app.py:84 - Page configuration completed
2025-07-09 10:32:48,521 - INFO - app - app.py:88 - Session state initialized
2025-07-09 10:32:48,521 - INFO - app - app.py:92 - User not authenticated, showing login form
2025-07-09 10:32:53,621 - INFO - app - app.py:82 - Starting application initialization
2025-07-09 10:32:53,625 - INFO - app - app.py:84 - Page configuration completed
2025-07-09 10:32:53,626 - INFO - app - app.py:88 - Session state initialized
2025-07-09 10:32:53,627 - INFO - app - app.py:92 - User not authenticated, showing login form
2025-07-09 10:32:55,219 - INFO - app - app.py:82 - Starting application initialization
2025-07-09 10:32:55,234 - INFO - app - app.py:84 - Page configuration completed
2025-07-09 10:32:55,234 - INFO - app - app.py:88 - Session state initialized
2025-07-09 10:32:55,234 - INFO - app - app.py:96 - User authenticated successfully
2025-07-09 10:32:55,235 - INFO - app - app.py:108 - Loading client data from database
2025-07-09 10:32:55,699 - INFO - app - app.py:117 - Successfully loaded data for 1 clients
2025-07-09 10:32:55,700 - INFO - app - app.py:65 - Successfully loaded image: logo/logo_integrum.jpg
2025-07-09 10:32:55,701 - INFO - app - app.py:148 - Client selection: None, Plant: None, Type: None
2025-07-09 10:32:55,701 - INFO - app - app.py:159 - Date range selected: 2025-07-09 to 2025-07-09
2025-07-09 10:32:55,706 - INFO - app - app.py:363 - No client selected, showing welcome message
2025-07-09 10:33:00,073 - INFO - app - app.py:82 - Starting application initialization
2025-07-09 10:33:00,073 - INFO - app - app.py:84 - Page configuration completed
2025-07-09 10:33:00,073 - INFO - app - app.py:88 - Session state initialized
2025-07-09 10:33:00,073 - INFO - app - app.py:96 - User authenticated successfully
2025-07-09 10:33:00,073 - INFO - app - app.py:108 - Loading client data from database
2025-07-09 10:33:00,159 - INFO - app - app.py:117 - Successfully loaded data for 1 clients
2025-07-09 10:33:00,160 - INFO - app - app.py:65 - Successfully loaded image: logo/logo_integrum.jpg
2025-07-09 10:33:00,162 - INFO - app - app.py:148 - Client selection: Kids Clinic India Limited, Plant: None, Type: Combined
2025-07-09 10:33:00,164 - INFO - app - app.py:159 - Date range selected: 2025-07-09 to 2025-07-09
2025-07-09 10:33:00,168 - INFO - app - app.py:187 - Created main application tabs
2025-07-09 10:33:00,168 - INFO - app - app.py:197 - Summary tab: Displaying data for Kids Clinic India Limited
2025-07-09 10:33:01,586 - INFO - app - app.py:212 - Successfully displayed generation vs consumption chart for Kids Clinic India Limited
2025-07-09 10:33:03,413 - INFO - app - app.py:222 - Successfully displayed monthly banking settlement for Kids Clinic India Limited
2025-07-09 10:33:03,522 - INFO - app - app.py:233 - Successfully displayed ToD generation analysis for Kids Clinic India Limited
2025-07-09 10:33:03,610 - INFO - app - app.py:244 - Successfully displayed ToD consumption analysis for Kids Clinic India Limited
2025-07-09 10:33:03,762 - INFO - app - app.py:255 - Successfully displayed hourly generation vs consumption chart for Kids Clinic India Limited
2025-07-09 10:33:03,866 - INFO - app - app.py:82 - Starting application initialization
2025-07-09 10:33:03,868 - INFO - app - app.py:84 - Page configuration completed
2025-07-09 10:33:03,870 - INFO - app - app.py:88 - Session state initialized
2025-07-09 10:33:03,871 - INFO - app - app.py:92 - User not authenticated, showing login form
2025-07-09 10:33:06,940 - INFO - app - app.py:277 - Successfully displayed unit-wise monthly bill analysis for Kids Clinic India Limited
2025-07-09 10:33:06,942 - INFO - app - app.py:289 - ToD Analysis tab: Displaying data for Kids Clinic India Limited
2025-07-09 10:33:07,187 - INFO - app - app.py:82 - Starting application initialization
2025-07-09 10:33:07,187 - INFO - app - app.py:84 - Page configuration completed
2025-07-09 10:33:07,187 - INFO - app - app.py:88 - Session state initialized
2025-07-09 10:33:07,187 - INFO - app - app.py:96 - User authenticated successfully
2025-07-09 10:33:07,187 - INFO - app - app.py:108 - Loading client data from database
2025-07-09 10:33:07,328 - INFO - app - app.py:117 - Successfully loaded data for 1 clients
2025-07-09 10:33:07,328 - INFO - app - app.py:65 - Successfully loaded image: logo/logo_integrum.jpg
2025-07-09 10:33:07,344 - INFO - app - app.py:148 - Client selection: Kids Clinic India Limited, Plant: None, Type: Combined
2025-07-09 10:33:07,346 - INFO - app - app.py:159 - Date range selected: 2025-04-01 to 2025-04-30
2025-07-09 10:33:07,365 - INFO - app - app.py:187 - Created main application tabs
2025-07-09 10:33:07,371 - INFO - app - app.py:197 - Summary tab: Displaying data for Kids Clinic India Limited
2025-07-09 10:33:08,327 - INFO - app - app.py:212 - Successfully displayed generation vs consumption chart for Kids Clinic India Limited
2025-07-09 10:33:10,074 - INFO - app - app.py:222 - Successfully displayed monthly banking settlement for Kids Clinic India Limited
2025-07-09 10:33:10,351 - INFO - app - app.py:233 - Successfully displayed ToD generation analysis for Kids Clinic India Limited
2025-07-09 10:33:10,577 - INFO - app - app.py:244 - Successfully displayed ToD consumption analysis for Kids Clinic India Limited
2025-07-09 10:33:11,736 - INFO - app - app.py:255 - Successfully displayed hourly generation vs consumption chart for Kids Clinic India Limited
2025-07-09 10:33:13,889 - INFO - app - app.py:277 - Successfully displayed unit-wise monthly bill analysis for Kids Clinic India Limited
2025-07-09 10:33:13,890 - INFO - app - app.py:289 - ToD Analysis tab: Displaying data for Kids Clinic India Limited
2025-07-09 10:33:14,335 - INFO - app - app.py:302 - Successfully displayed monthly ToD before banking for Kids Clinic India Limited
2025-07-09 10:33:15,404 - INFO - app - app.py:312 - Successfully displayed ToD generation vs consumption for Kids Clinic India Limited
2025-07-09 10:33:15,573 - INFO - app - app.py:82 - Starting application initialization
2025-07-09 10:33:15,576 - INFO - app - app.py:84 - Page configuration completed
2025-07-09 10:33:15,576 - INFO - app - app.py:88 - Session state initialized
2025-07-09 10:33:15,576 - INFO - app - app.py:92 - User not authenticated, showing login form
2025-07-09 10:33:15,798 - INFO - app - app.py:323 - Successfully displayed ToD line chart for Kids Clinic India Limited
2025-07-09 10:33:16,389 - INFO - app - app.py:334 - Successfully displayed generation vs consumption settled heatmap for Kids Clinic India Limited
2025-07-09 10:33:17,203 - INFO - app - app.py:82 - Starting application initialization
2025-07-09 10:33:17,203 - INFO - app - app.py:84 - Page configuration completed
2025-07-09 10:33:17,203 - INFO - app - app.py:88 - Session state initialized
2025-07-09 10:33:17,219 - INFO - app - app.py:96 - User authenticated successfully
2025-07-09 10:33:17,221 - INFO - app - app.py:108 - Loading client data from database
2025-07-09 10:33:17,353 - INFO - app - app.py:117 - Successfully loaded data for 1 clients
2025-07-09 10:33:17,353 - INFO - app - app.py:65 - Successfully loaded image: logo/logo_integrum.jpg
2025-07-09 10:33:17,353 - INFO - app - app.py:148 - Client selection: None, Plant: None, Type: None
2025-07-09 10:33:17,369 - INFO - app - app.py:159 - Date range selected: 2025-07-09 to 2025-07-09
2025-07-09 10:33:17,371 - INFO - app - app.py:363 - No client selected, showing welcome message
2025-07-09 10:33:20,789 - INFO - app - app.py:82 - Starting application initialization
2025-07-09 10:33:20,792 - INFO - app - app.py:84 - Page configuration completed
2025-07-09 10:33:20,792 - INFO - app - app.py:88 - Session state initialized
2025-07-09 10:33:20,792 - INFO - app - app.py:96 - User authenticated successfully
2025-07-09 10:33:20,792 - INFO - app - app.py:108 - Loading client data from database
2025-07-09 10:33:20,879 - INFO - app - app.py:117 - Successfully loaded data for 1 clients
2025-07-09 10:33:20,880 - INFO - app - app.py:65 - Successfully loaded image: logo/logo_integrum.jpg
2025-07-09 10:33:20,884 - INFO - app - app.py:148 - Client selection: Kids Clinic India Limited, Plant: None, Type: Combined
2025-07-09 10:33:20,886 - INFO - app - app.py:159 - Date range selected: 2025-07-09 to 2025-07-09
2025-07-09 10:33:20,890 - INFO - app - app.py:187 - Created main application tabs
2025-07-09 10:33:20,891 - INFO - app - app.py:197 - Summary tab: Displaying data for Kids Clinic India Limited
2025-07-09 10:33:21,889 - INFO - app - app.py:212 - Successfully displayed generation vs consumption chart for Kids Clinic India Limited
2025-07-09 10:33:23,677 - INFO - app - app.py:222 - Successfully displayed monthly banking settlement for Kids Clinic India Limited
2025-07-09 10:33:23,769 - INFO - app - app.py:233 - Successfully displayed ToD generation analysis for Kids Clinic India Limited
2025-07-09 10:33:23,848 - INFO - app - app.py:244 - Successfully displayed ToD consumption analysis for Kids Clinic India Limited
2025-07-09 10:33:23,937 - INFO - app - app.py:255 - Successfully displayed hourly generation vs consumption chart for Kids Clinic India Limited
2025-07-09 10:33:25,922 - INFO - app - app.py:82 - Starting application initialization
2025-07-09 10:33:25,953 - INFO - app - app.py:84 - Page configuration completed
2025-07-09 10:33:25,963 - INFO - app - app.py:88 - Session state initialized
2025-07-09 10:33:26,064 - INFO - app - app.py:96 - User authenticated successfully
2025-07-09 10:33:26,064 - INFO - app - app.py:108 - Loading client data from database
2025-07-09 10:33:26,257 - INFO - app - app.py:117 - Successfully loaded data for 1 clients
2025-07-09 10:33:26,257 - INFO - app - app.py:65 - Successfully loaded image: logo/logo_integrum.jpg
2025-07-09 10:33:26,258 - INFO - app - app.py:148 - Client selection: Kids Clinic India Limited, Plant: None, Type: Combined
2025-07-09 10:33:26,258 - INFO - app - app.py:159 - Date range selected: 2025-05-01 to 2025-05-01
2025-07-09 10:33:26,270 - INFO - app - app.py:187 - Created main application tabs
2025-07-09 10:33:26,270 - INFO - app - app.py:197 - Summary tab: Displaying data for Kids Clinic India Limited
2025-07-09 10:33:26,621 - INFO - app - app.py:82 - Starting application initialization
2025-07-09 10:33:26,621 - INFO - app - app.py:84 - Page configuration completed
2025-07-09 10:33:26,621 - INFO - app - app.py:88 - Session state initialized
2025-07-09 10:33:26,621 - INFO - app - app.py:96 - User authenticated successfully
2025-07-09 10:33:26,621 - INFO - app - app.py:108 - Loading client data from database
2025-07-09 10:33:26,901 - INFO - app - app.py:117 - Successfully loaded data for 1 clients
2025-07-09 10:33:26,904 - INFO - app - app.py:65 - Successfully loaded image: logo/logo_integrum.jpg
2025-07-09 10:33:26,905 - INFO - app - app.py:148 - Client selection: Kids Clinic India Limited, Plant: None, Type: Combined
2025-07-09 10:33:26,908 - INFO - app - app.py:159 - Date range selected: 2025-05-01 to 2025-05-31
2025-07-09 10:33:26,908 - INFO - app - app.py:187 - Created main application tabs
2025-07-09 10:33:26,908 - INFO - app - app.py:197 - Summary tab: Displaying data for Kids Clinic India Limited
2025-07-09 10:33:27,826 - INFO - app - app.py:212 - Successfully displayed generation vs consumption chart for Kids Clinic India Limited
2025-07-09 10:33:29,528 - INFO - app - app.py:222 - Successfully displayed monthly banking settlement for Kids Clinic India Limited
2025-07-09 10:33:29,748 - INFO - app - app.py:233 - Successfully displayed ToD generation analysis for Kids Clinic India Limited
2025-07-09 10:33:30,023 - INFO - app - app.py:244 - Successfully displayed ToD consumption analysis for Kids Clinic India Limited
2025-07-09 10:33:30,364 - INFO - app - app.py:255 - Successfully displayed hourly generation vs consumption chart for Kids Clinic India Limited
2025-07-09 10:33:32,634 - INFO - app - app.py:277 - Successfully displayed unit-wise monthly bill analysis for Kids Clinic India Limited
2025-07-09 10:33:32,634 - INFO - app - app.py:289 - ToD Analysis tab: Displaying data for Kids Clinic India Limited
2025-07-09 10:33:33,050 - INFO - app - app.py:302 - Successfully displayed monthly ToD before banking for Kids Clinic India Limited
2025-07-09 10:33:33,787 - INFO - app - app.py:312 - Successfully displayed ToD generation vs consumption for Kids Clinic India Limited
2025-07-09 10:33:34,022 - INFO - app - app.py:323 - Successfully displayed ToD line chart for Kids Clinic India Limited
2025-07-09 10:33:34,521 - INFO - app - app.py:334 - Successfully displayed generation vs consumption settled heatmap for Kids Clinic India Limited
2025-07-09 10:37:16,620 - INFO - app - app.py:82 - Starting application initialization
2025-07-09 10:37:16,682 - INFO - app - app.py:84 - Page configuration completed
2025-07-09 10:37:16,682 - INFO - app - app.py:88 - Session state initialized
2025-07-09 10:37:16,682 - INFO - app - app.py:92 - User not authenticated, showing login form
2025-07-09 10:37:18,035 - INFO - app - app.py:82 - Starting application initialization
2025-07-09 10:37:18,072 - INFO - app - app.py:84 - Page configuration completed
2025-07-09 10:37:18,072 - INFO - app - app.py:88 - Session state initialized
2025-07-09 10:37:18,072 - INFO - app - app.py:92 - User not authenticated, showing login form
2025-07-09 10:37:20,095 - INFO - app - app.py:82 - Starting application initialization
2025-07-09 10:37:20,098 - INFO - app - app.py:84 - Page configuration completed
2025-07-09 10:37:20,098 - INFO - app - app.py:88 - Session state initialized
2025-07-09 10:37:20,099 - INFO - app - app.py:92 - User not authenticated, showing login form
2025-07-09 10:37:21,655 - INFO - app - app.py:82 - Starting application initialization
2025-07-09 10:37:21,655 - INFO - app - app.py:84 - Page configuration completed
2025-07-09 10:37:21,655 - INFO - app - app.py:88 - Session state initialized
2025-07-09 10:37:21,655 - INFO - app - app.py:96 - User authenticated successfully
2025-07-09 10:37:21,655 - INFO - app - app.py:108 - Loading client data from database
2025-07-09 10:37:21,873 - INFO - app - app.py:117 - Successfully loaded data for 1 clients
2025-07-09 10:37:21,873 - INFO - app - app.py:65 - Successfully loaded image: logo/logo_integrum.jpg
2025-07-09 10:37:21,873 - INFO - app - app.py:148 - Client selection: None, Plant: None, Type: None
2025-07-09 10:37:21,873 - INFO - app - app.py:159 - Date range selected: 2025-07-09 to 2025-07-09
2025-07-09 10:37:21,888 - INFO - app - app.py:363 - No client selected, showing welcome message
2025-07-09 10:37:24,165 - INFO - app - app.py:82 - Starting application initialization
2025-07-09 10:37:24,165 - INFO - app - app.py:84 - Page configuration completed
2025-07-09 10:37:24,165 - INFO - app - app.py:88 - Session state initialized
2025-07-09 10:37:24,165 - INFO - app - app.py:96 - User authenticated successfully
2025-07-09 10:37:24,165 - INFO - app - app.py:108 - Loading client data from database
2025-07-09 10:37:24,250 - INFO - app - app.py:117 - Successfully loaded data for 1 clients
2025-07-09 10:37:24,251 - INFO - app - app.py:65 - Successfully loaded image: logo/logo_integrum.jpg
2025-07-09 10:37:24,252 - INFO - app - app.py:148 - Client selection: Kids Clinic India Limited, Plant: None, Type: Combined
2025-07-09 10:37:24,254 - INFO - app - app.py:159 - Date range selected: 2025-07-09 to 2025-07-09
2025-07-09 10:37:24,258 - INFO - app - app.py:187 - Created main application tabs
2025-07-09 10:37:24,258 - INFO - app - app.py:197 - Summary tab: Displaying data for Kids Clinic India Limited
2025-07-09 10:37:25,358 - INFO - app - app.py:212 - Successfully displayed generation vs consumption chart for Kids Clinic India Limited
2025-07-09 10:37:26,391 - INFO - app - app.py:222 - Successfully displayed monthly banking settlement for Kids Clinic India Limited
2025-07-09 10:37:26,451 - INFO - app - app.py:233 - Successfully displayed ToD generation analysis for Kids Clinic India Limited
2025-07-09 10:37:26,526 - INFO - app - app.py:244 - Successfully displayed ToD consumption analysis for Kids Clinic India Limited
2025-07-09 10:37:26,629 - INFO - app - app.py:255 - Successfully displayed hourly generation vs consumption chart for Kids Clinic India Limited
2025-07-09 10:37:27,931 - INFO - app - app.py:277 - Successfully displayed unit-wise monthly bill analysis for Kids Clinic India Limited
2025-07-09 10:37:27,931 - INFO - app - app.py:289 - ToD Analysis tab: Displaying data for Kids Clinic India Limited
2025-07-09 10:37:28,289 - INFO - app - app.py:302 - Successfully displayed monthly ToD before banking for Kids Clinic India Limited
2025-07-09 10:37:28,367 - INFO - app - app.py:312 - Successfully displayed ToD generation vs consumption for Kids Clinic India Limited
2025-07-09 10:37:28,393 - INFO - app - app.py:82 - Starting application initialization
2025-07-09 10:37:28,395 - INFO - app - app.py:84 - Page configuration completed
2025-07-09 10:37:28,395 - INFO - app - app.py:88 - Session state initialized
2025-07-09 10:37:28,395 - INFO - app - app.py:96 - User authenticated successfully
2025-07-09 10:37:28,397 - INFO - app - app.py:108 - Loading client data from database
2025-07-09 10:37:28,514 - INFO - app - app.py:117 - Successfully loaded data for 1 clients
2025-07-09 10:37:28,515 - INFO - app - app.py:65 - Successfully loaded image: logo/logo_integrum.jpg
2025-07-09 10:37:28,516 - INFO - app - app.py:148 - Client selection: Kids Clinic India Limited, Plant: None, Type: Combined
2025-07-09 10:37:28,518 - INFO - app - app.py:159 - Date range selected: 2025-04-01 to 2025-04-01
2025-07-09 10:37:28,522 - INFO - app - app.py:187 - Created main application tabs
2025-07-09 10:37:28,523 - INFO - app - app.py:197 - Summary tab: Displaying data for Kids Clinic India Limited
2025-07-09 10:37:29,305 - INFO - app - app.py:82 - Starting application initialization
2025-07-09 10:37:29,305 - INFO - app - app.py:84 - Page configuration completed
2025-07-09 10:37:29,305 - INFO - app - app.py:88 - Session state initialized
2025-07-09 10:37:29,305 - INFO - app - app.py:96 - User authenticated successfully
2025-07-09 10:37:29,305 - INFO - app - app.py:108 - Loading client data from database
2025-07-09 10:37:29,477 - INFO - app - app.py:117 - Successfully loaded data for 1 clients
2025-07-09 10:37:29,477 - INFO - app - app.py:65 - Successfully loaded image: logo/logo_integrum.jpg
2025-07-09 10:37:29,489 - INFO - app - app.py:148 - Client selection: Kids Clinic India Limited, Plant: None, Type: Combined
2025-07-09 10:37:29,491 - INFO - app - app.py:159 - Date range selected: 2025-04-01 to 2025-04-30
2025-07-09 10:37:29,494 - INFO - app - app.py:187 - Created main application tabs
2025-07-09 10:37:29,494 - INFO - app - app.py:197 - Summary tab: Displaying data for Kids Clinic India Limited
2025-07-09 10:37:30,339 - INFO - app - app.py:212 - Successfully displayed generation vs consumption chart for Kids Clinic India Limited
2025-07-09 10:37:31,299 - INFO - app - app.py:222 - Successfully displayed monthly banking settlement for Kids Clinic India Limited
2025-07-09 10:37:31,512 - INFO - app - app.py:233 - Successfully displayed ToD generation analysis for Kids Clinic India Limited
2025-07-09 10:37:31,724 - INFO - app - app.py:244 - Successfully displayed ToD consumption analysis for Kids Clinic India Limited
2025-07-09 10:37:32,186 - INFO - app - app.py:255 - Successfully displayed hourly generation vs consumption chart for Kids Clinic India Limited
2025-07-09 10:37:33,435 - INFO - app - app.py:277 - Successfully displayed unit-wise monthly bill analysis for Kids Clinic India Limited
2025-07-09 10:37:33,435 - INFO - app - app.py:289 - ToD Analysis tab: Displaying data for Kids Clinic India Limited
2025-07-09 10:37:33,773 - INFO - app - app.py:302 - Successfully displayed monthly ToD before banking for Kids Clinic India Limited
2025-07-09 10:37:34,223 - INFO - app - app.py:312 - Successfully displayed ToD generation vs consumption for Kids Clinic India Limited
2025-07-09 10:37:34,477 - INFO - app - app.py:323 - Successfully displayed ToD line chart for Kids Clinic India Limited
2025-07-09 10:37:35,187 - INFO - app - app.py:82 - Starting application initialization
2025-07-09 10:37:35,190 - INFO - app - app.py:84 - Page configuration completed
2025-07-09 10:37:35,190 - INFO - app - app.py:88 - Session state initialized
2025-07-09 10:37:35,190 - INFO - app - app.py:92 - User not authenticated, showing login form
2025-07-09 10:37:35,289 - INFO - app - app.py:334 - Successfully displayed generation vs consumption settled heatmap for Kids Clinic India Limited
2025-07-09 10:37:55,081 - INFO - app - app.py:82 - Starting application initialization
2025-07-09 10:37:55,081 - INFO - app - app.py:84 - Page configuration completed
2025-07-09 10:37:55,081 - INFO - app - app.py:88 - Session state initialized
2025-07-09 10:37:55,081 - INFO - app - app.py:96 - User authenticated successfully
2025-07-09 10:37:55,081 - INFO - app - app.py:108 - Loading client data from database
2025-07-09 10:37:55,206 - INFO - app - app.py:117 - Successfully loaded data for 1 clients
2025-07-09 10:37:55,206 - INFO - app - app.py:65 - Successfully loaded image: logo/logo_integrum.jpg
2025-07-09 10:37:55,206 - INFO - app - app.py:148 - Client selection: Kids Clinic India Limited, Plant: None, Type: Combined
2025-07-09 10:37:55,206 - INFO - app - app.py:159 - Date range selected: 2025-04-01 to 2025-04-30
2025-07-09 10:37:55,206 - INFO - app - app.py:187 - Created main application tabs
2025-07-09 10:37:55,206 - INFO - app - app.py:197 - Summary tab: Displaying data for Kids Clinic India Limited
2025-07-09 10:37:56,108 - INFO - app - app.py:212 - Successfully displayed generation vs consumption chart for Kids Clinic India Limited
2025-07-09 10:37:57,051 - INFO - app - app.py:222 - Successfully displayed monthly banking settlement for Kids Clinic India Limited
2025-07-09 10:37:57,239 - INFO - app - app.py:233 - Successfully displayed ToD generation analysis for Kids Clinic India Limited
2025-07-09 10:37:57,445 - INFO - app - app.py:244 - Successfully displayed ToD consumption analysis for Kids Clinic India Limited
2025-07-09 10:37:57,693 - INFO - app - app.py:255 - Successfully displayed hourly generation vs consumption chart for Kids Clinic India Limited
2025-07-09 10:38:12,026 - INFO - app - app.py:82 - Starting application initialization
2025-07-09 10:38:12,026 - INFO - app - app.py:82 - Starting application initialization
2025-07-09 10:38:12,026 - INFO - app - app.py:82 - Starting application initialization
2025-07-09 10:38:12,026 - INFO - app - app.py:82 - Starting application initialization
2025-07-09 10:38:12,073 - INFO - app - app.py:84 - Page configuration completed
2025-07-09 10:38:12,073 - INFO - app - app.py:84 - Page configuration completed
2025-07-09 10:38:12,073 - INFO - app - app.py:84 - Page configuration completed
2025-07-09 10:38:12,073 - INFO - app - app.py:88 - Session state initialized
2025-07-09 10:38:12,073 - INFO - app - app.py:84 - Page configuration completed
2025-07-09 10:38:12,073 - INFO - app - app.py:88 - Session state initialized
2025-07-09 10:38:12,073 - INFO - app - app.py:88 - Session state initialized
2025-07-09 10:38:12,073 - INFO - app - app.py:88 - Session state initialized
2025-07-09 10:38:12,073 - INFO - app - app.py:92 - User not authenticated, showing login form
2025-07-09 10:38:12,073 - INFO - app - app.py:92 - User not authenticated, showing login form
2025-07-09 10:38:12,073 - INFO - app - app.py:92 - User not authenticated, showing login form
2025-07-09 10:38:12,073 - INFO - app - app.py:92 - User not authenticated, showing login form
2025-07-09 10:38:20,627 - INFO - app - app.py:82 - Starting application initialization
2025-07-09 10:38:20,627 - INFO - app - app.py:84 - Page configuration completed
2025-07-09 10:38:20,627 - INFO - app - app.py:88 - Session state initialized
2025-07-09 10:38:20,627 - INFO - app - app.py:92 - User not authenticated, showing login form
2025-07-09 10:38:20,779 - INFO - app - app.py:82 - Starting application initialization
2025-07-09 10:38:20,780 - INFO - app - app.py:84 - Page configuration completed
2025-07-09 10:38:20,780 - INFO - app - app.py:88 - Session state initialized
2025-07-09 10:38:20,780 - INFO - app - app.py:92 - User not authenticated, showing login form
2025-07-09 10:38:22,399 - INFO - app - app.py:82 - Starting application initialization
2025-07-09 10:38:22,399 - INFO - app - app.py:84 - Page configuration completed
2025-07-09 10:38:22,399 - INFO - app - app.py:88 - Session state initialized
2025-07-09 10:38:22,399 - INFO - app - app.py:96 - User authenticated successfully
2025-07-09 10:38:22,408 - INFO - app - app.py:108 - Loading client data from database
2025-07-09 10:38:22,759 - INFO - app - app.py:117 - Successfully loaded data for 1 clients
2025-07-09 10:38:22,775 - INFO - app - app.py:65 - Successfully loaded image: logo/logo_integrum.jpg
2025-07-09 10:38:22,775 - INFO - app - app.py:148 - Client selection: None, Plant: None, Type: None
2025-07-09 10:38:22,775 - INFO - app - app.py:159 - Date range selected: 2025-07-09 to 2025-07-09
2025-07-09 10:38:22,775 - INFO - app - app.py:363 - No client selected, showing welcome message
2025-07-09 10:38:24,701 - INFO - app - app.py:82 - Starting application initialization
2025-07-09 10:38:24,701 - INFO - app - app.py:84 - Page configuration completed
2025-07-09 10:38:24,701 - INFO - app - app.py:88 - Session state initialized
2025-07-09 10:38:24,701 - INFO - app - app.py:96 - User authenticated successfully
2025-07-09 10:38:24,701 - INFO - app - app.py:108 - Loading client data from database
2025-07-09 10:38:24,811 - INFO - app - app.py:117 - Successfully loaded data for 1 clients
2025-07-09 10:38:24,812 - INFO - app - app.py:65 - Successfully loaded image: logo/logo_integrum.jpg
2025-07-09 10:38:24,813 - INFO - app - app.py:148 - Client selection: Kids Clinic India Limited, Plant: None, Type: Combined
2025-07-09 10:38:24,815 - INFO - app - app.py:159 - Date range selected: 2025-07-09 to 2025-07-09
2025-07-09 10:38:24,821 - INFO - app - app.py:187 - Created main application tabs
2025-07-09 10:38:24,821 - INFO - app - app.py:197 - Summary tab: Displaying data for Kids Clinic India Limited
2025-07-09 10:38:26,045 - INFO - app - app.py:212 - Successfully displayed generation vs consumption chart for Kids Clinic India Limited
2025-07-09 10:38:27,852 - INFO - app - app.py:82 - Starting application initialization
2025-07-09 10:38:27,852 - INFO - app - app.py:84 - Page configuration completed
2025-07-09 10:38:27,861 - INFO - app - app.py:88 - Session state initialized
2025-07-09 10:38:27,863 - INFO - app - app.py:96 - User authenticated successfully
2025-07-09 10:38:27,944 - INFO - app - app.py:108 - Loading client data from database
2025-07-09 10:38:28,050 - INFO - app - app.py:117 - Successfully loaded data for 1 clients
2025-07-09 10:38:28,051 - INFO - app - app.py:65 - Successfully loaded image: logo/logo_integrum.jpg
2025-07-09 10:38:28,053 - INFO - app - app.py:148 - Client selection: Kids Clinic India Limited, Plant: None, Type: Combined
2025-07-09 10:38:28,055 - INFO - app - app.py:159 - Date range selected: 2025-04-01 to 2025-04-01
2025-07-09 10:38:28,061 - INFO - app - app.py:187 - Created main application tabs
2025-07-09 10:38:28,062 - INFO - app - app.py:197 - Summary tab: Displaying data for Kids Clinic India Limited
2025-07-09 10:38:28,842 - INFO - app - app.py:82 - Starting application initialization
2025-07-09 10:38:28,843 - INFO - app - app.py:84 - Page configuration completed
2025-07-09 10:38:28,844 - INFO - app - app.py:88 - Session state initialized
2025-07-09 10:38:28,844 - INFO - app - app.py:96 - User authenticated successfully
2025-07-09 10:38:28,846 - INFO - app - app.py:108 - Loading client data from database
2025-07-09 10:38:28,959 - INFO - app - app.py:117 - Successfully loaded data for 1 clients
2025-07-09 10:38:28,959 - INFO - app - app.py:65 - Successfully loaded image: logo/logo_integrum.jpg
2025-07-09 10:38:29,079 - INFO - app - app.py:148 - Client selection: Kids Clinic India Limited, Plant: None, Type: Combined
2025-07-09 10:38:29,079 - INFO - app - app.py:159 - Date range selected: 2025-04-01 to 2025-04-30
2025-07-09 10:38:29,092 - INFO - app - app.py:187 - Created main application tabs
2025-07-09 10:38:29,092 - INFO - app - app.py:197 - Summary tab: Displaying data for Kids Clinic India Limited
2025-07-09 10:38:29,977 - INFO - app - app.py:212 - Successfully displayed generation vs consumption chart for Kids Clinic India Limited
2025-07-09 10:38:31,679 - INFO - app - app.py:222 - Successfully displayed monthly banking settlement for Kids Clinic India Limited
2025-07-09 10:38:32,019 - INFO - app - app.py:233 - Successfully displayed ToD generation analysis for Kids Clinic India Limited
2025-07-09 10:38:32,298 - INFO - app - app.py:244 - Successfully displayed ToD consumption analysis for Kids Clinic India Limited
2025-07-09 10:38:33,130 - INFO - app - app.py:255 - Successfully displayed hourly generation vs consumption chart for Kids Clinic India Limited
2025-07-09 10:38:35,361 - INFO - app - app.py:277 - Successfully displayed unit-wise monthly bill analysis for Kids Clinic India Limited
2025-07-09 10:38:35,361 - INFO - app - app.py:289 - ToD Analysis tab: Displaying data for Kids Clinic India Limited
2025-07-09 10:38:35,778 - INFO - app - app.py:302 - Successfully displayed monthly ToD before banking for Kids Clinic India Limited
2025-07-09 10:38:36,629 - INFO - app - app.py:312 - Successfully displayed ToD generation vs consumption for Kids Clinic India Limited
2025-07-09 10:38:36,878 - INFO - app - app.py:323 - Successfully displayed ToD line chart for Kids Clinic India Limited
2025-07-09 10:38:37,466 - INFO - app - app.py:334 - Successfully displayed generation vs consumption settled heatmap for Kids Clinic India Limited
2025-07-09 12:04:26,319 - INFO - app - app.py:82 - Starting application initialization
2025-07-09 12:04:26,333 - INFO - app - app.py:84 - Page configuration completed
2025-07-09 12:04:26,335 - INFO - app - app.py:88 - Session state initialized
2025-07-09 12:04:26,335 - INFO - app - app.py:96 - User authenticated successfully
2025-07-09 12:04:26,335 - INFO - app - app.py:108 - Loading client data from database
2025-07-09 12:04:26,634 - INFO - app - app.py:117 - Successfully loaded data for 1 clients
2025-07-09 12:04:26,634 - INFO - app - app.py:65 - Successfully loaded image: logo/logo_integrum.jpg
2025-07-09 12:04:26,634 - INFO - app - app.py:148 - Client selection: Kids Clinic India Limited, Plant: None, Type: Combined
2025-07-09 12:04:26,634 - INFO - app - app.py:159 - Date range selected: 2025-04-01 to 2025-04-30
2025-07-09 12:04:26,644 - INFO - app - app.py:187 - Created main application tabs
2025-07-09 12:04:26,644 - INFO - app - app.py:197 - Summary tab: Displaying data for Kids Clinic India Limited
2025-07-09 12:04:28,814 - INFO - app - app.py:212 - Successfully displayed generation vs consumption chart for Kids Clinic India Limited
2025-07-09 12:04:31,728 - INFO - app - app.py:222 - Successfully displayed monthly banking settlement for Kids Clinic India Limited
2025-07-09 12:04:32,485 - INFO - app - app.py:233 - Successfully displayed ToD generation analysis for Kids Clinic India Limited
2025-07-09 12:04:33,157 - INFO - app - app.py:244 - Successfully displayed ToD consumption analysis for Kids Clinic India Limited
2025-07-09 12:04:33,986 - INFO - app - app.py:255 - Successfully displayed hourly generation vs consumption chart for Kids Clinic India Limited
2025-07-09 12:04:37,983 - INFO - app - app.py:277 - Successfully displayed unit-wise monthly bill analysis for Kids Clinic India Limited
2025-07-09 12:04:37,983 - INFO - app - app.py:289 - ToD Analysis tab: Displaying data for Kids Clinic India Limited
2025-07-09 12:04:39,078 - INFO - app - app.py:302 - Successfully displayed monthly ToD before banking for Kids Clinic India Limited
2025-07-09 12:04:40,591 - INFO - app - app.py:312 - Successfully displayed ToD generation vs consumption for Kids Clinic India Limited
2025-07-09 12:04:41,165 - INFO - app - app.py:323 - Successfully displayed ToD line chart for Kids Clinic India Limited
2025-07-09 12:04:42,312 - INFO - app - app.py:334 - Successfully displayed generation vs consumption settled heatmap for Kids Clinic India Limited
2025-07-09 12:04:42,313 - INFO - app - app.py:347 - Power Cost Analysis tab: Displaying data for Kids Clinic India Limited
2025-07-09 12:04:45,381 - INFO - app - app.py:352 - Successfully displayed power cost analysis for Kids Clinic India Limited
2025-07-09 12:06:40,671 - INFO - app - app.py:82 - Starting application initialization
2025-07-09 12:06:40,673 - INFO - app - app.py:84 - Page configuration completed
2025-07-09 12:06:40,673 - INFO - app - app.py:88 - Session state initialized
2025-07-09 12:06:40,673 - INFO - app - app.py:96 - User authenticated successfully
2025-07-09 12:06:40,675 - INFO - app - app.py:108 - Loading client data from database
2025-07-09 12:06:40,953 - INFO - app - app.py:117 - Successfully loaded data for 1 clients
2025-07-09 12:06:40,953 - INFO - app - app.py:65 - Successfully loaded image: logo/logo_integrum.jpg
2025-07-09 12:06:40,953 - INFO - app - app.py:148 - Client selection: Kids Clinic India Limited, Plant: None, Type: Combined
2025-07-09 12:06:40,953 - INFO - app - app.py:159 - Date range selected: 2025-04-01 to 2025-04-30
2025-07-09 12:06:40,963 - INFO - app - app.py:187 - Created main application tabs
2025-07-09 12:06:40,964 - INFO - app - app.py:197 - Summary tab: Displaying data for Kids Clinic India Limited
2025-07-09 12:06:42,989 - INFO - app - app.py:212 - Successfully displayed generation vs consumption chart for Kids Clinic India Limited
2025-07-09 12:06:44,321 - INFO - app - app.py:222 - Successfully displayed monthly banking settlement for Kids Clinic India Limited
2025-07-09 12:06:44,809 - INFO - app - app.py:233 - Successfully displayed ToD generation analysis for Kids Clinic India Limited
2025-07-09 12:06:45,339 - INFO - app - app.py:244 - Successfully displayed ToD consumption analysis for Kids Clinic India Limited
2025-07-09 12:06:45,899 - INFO - app - app.py:255 - Successfully displayed hourly generation vs consumption chart for Kids Clinic India Limited
2025-07-09 12:06:47,521 - INFO - app - app.py:277 - Successfully displayed unit-wise monthly bill analysis for Kids Clinic India Limited
2025-07-09 12:06:47,521 - INFO - app - app.py:289 - ToD Analysis tab: Displaying data for Kids Clinic India Limited
2025-07-09 12:06:48,304 - INFO - app - app.py:302 - Successfully displayed monthly ToD before banking for Kids Clinic India Limited
2025-07-09 12:06:49,182 - INFO - app - app.py:312 - Successfully displayed ToD generation vs consumption for Kids Clinic India Limited
2025-07-09 12:06:49,692 - INFO - app - app.py:323 - Successfully displayed ToD line chart for Kids Clinic India Limited
2025-07-09 12:06:50,727 - INFO - app - app.py:334 - Successfully displayed generation vs consumption settled heatmap for Kids Clinic India Limited
2025-07-09 12:06:50,727 - INFO - app - app.py:347 - Power Cost Analysis tab: Displaying data for Kids Clinic India Limited
2025-07-09 12:06:52,000 - INFO - app - app.py:352 - Successfully displayed power cost analysis for Kids Clinic India Limited
2025-07-09 12:07:12,773 - INFO - app - app.py:82 - Starting application initialization
2025-07-09 12:07:12,775 - INFO - app - app.py:84 - Page configuration completed
2025-07-09 12:07:12,776 - INFO - app - app.py:88 - Session state initialized
2025-07-09 12:07:12,776 - INFO - app - app.py:96 - User authenticated successfully
2025-07-09 12:07:12,776 - INFO - app - app.py:108 - Loading client data from database
2025-07-09 12:07:13,041 - INFO - app - app.py:117 - Successfully loaded data for 1 clients
2025-07-09 12:07:13,041 - INFO - app - app.py:65 - Successfully loaded image: logo/logo_integrum.jpg
2025-07-09 12:07:13,041 - INFO - app - app.py:148 - Client selection: Kids Clinic India Limited, Plant: None, Type: Combined
2025-07-09 12:07:13,041 - INFO - app - app.py:159 - Date range selected: 2025-04-01 to 2025-04-30
2025-07-09 12:07:13,052 - INFO - app - app.py:187 - Created main application tabs
2025-07-09 12:07:13,054 - INFO - app - app.py:197 - Summary tab: Displaying data for Kids Clinic India Limited
2025-07-09 12:07:15,192 - INFO - app - app.py:212 - Successfully displayed generation vs consumption chart for Kids Clinic India Limited
2025-07-09 12:07:16,358 - INFO - app - app.py:222 - Successfully displayed monthly banking settlement for Kids Clinic India Limited
2025-07-09 12:07:16,829 - INFO - app - app.py:233 - Successfully displayed ToD generation analysis for Kids Clinic India Limited
2025-07-09 12:07:17,368 - INFO - app - app.py:244 - Successfully displayed ToD consumption analysis for Kids Clinic India Limited
2025-07-09 12:07:17,915 - INFO - app - app.py:255 - Successfully displayed hourly generation vs consumption chart for Kids Clinic India Limited
2025-07-09 12:07:20,840 - INFO - app - app.py:277 - Successfully displayed unit-wise monthly bill analysis for Kids Clinic India Limited
2025-07-09 12:07:20,840 - INFO - app - app.py:289 - ToD Analysis tab: Displaying data for Kids Clinic India Limited
2025-07-09 12:07:21,792 - INFO - app - app.py:302 - Successfully displayed monthly ToD before banking for Kids Clinic India Limited
2025-07-09 12:07:23,158 - INFO - app - app.py:312 - Successfully displayed ToD generation vs consumption for Kids Clinic India Limited
2025-07-09 12:07:23,754 - INFO - app - app.py:323 - Successfully displayed ToD line chart for Kids Clinic India Limited
2025-07-09 12:07:24,959 - INFO - app - app.py:334 - Successfully displayed generation vs consumption settled heatmap for Kids Clinic India Limited
2025-07-09 12:07:24,959 - INFO - app - app.py:347 - Power Cost Analysis tab: Displaying data for Kids Clinic India Limited
2025-07-09 12:07:27,751 - INFO - app - app.py:352 - Successfully displayed power cost analysis for Kids Clinic India Limited
2025-07-09 14:01:48,211 - INFO - app - app.py:82 - Starting application initialization
2025-07-09 14:01:48,319 - INFO - app - app.py:84 - Page configuration completed
2025-07-09 14:01:48,319 - INFO - app - app.py:88 - Session state initialized
2025-07-09 14:01:48,319 - INFO - app - app.py:92 - User not authenticated, showing login form
2025-07-09 14:02:02,883 - INFO - app - app.py:82 - Starting application initialization
2025-07-09 14:02:02,888 - INFO - app - app.py:84 - Page configuration completed
2025-07-09 14:02:02,888 - INFO - app - app.py:88 - Session state initialized
2025-07-09 14:02:02,890 - INFO - app - app.py:92 - User not authenticated, showing login form
2025-07-09 14:02:32,906 - INFO - app - app.py:82 - Starting application initialization
2025-07-09 14:02:32,906 - INFO - app - app.py:84 - Page configuration completed
2025-07-09 14:02:32,906 - INFO - app - app.py:88 - Session state initialized
2025-07-09 14:02:32,906 - INFO - app - app.py:92 - User not authenticated, showing login form
2025-07-09 14:02:33,125 - INFO - app - app.py:82 - Starting application initialization
2025-07-09 14:02:33,128 - INFO - app - app.py:84 - Page configuration completed
2025-07-09 14:02:33,129 - INFO - app - app.py:88 - Session state initialized
2025-07-09 14:02:33,129 - INFO - app - app.py:92 - User not authenticated, showing login form
2025-07-09 14:02:34,763 - INFO - app - app.py:82 - Starting application initialization
2025-07-09 14:02:34,763 - INFO - app - app.py:84 - Page configuration completed
2025-07-09 14:02:34,763 - INFO - app - app.py:88 - Session state initialized
2025-07-09 14:02:34,763 - INFO - app - app.py:96 - User authenticated successfully
2025-07-09 14:02:34,770 - INFO - app - app.py:108 - Loading client data from database
2025-07-09 14:02:35,405 - INFO - app - app.py:117 - Successfully loaded data for 1 clients
2025-07-09 14:02:35,405 - INFO - app - app.py:65 - Successfully loaded image: logo/logo_integrum.jpg
2025-07-09 14:02:35,405 - INFO - app - app.py:148 - Client selection: None, Plant: None, Type: None
2025-07-09 14:02:35,405 - INFO - app - app.py:159 - Date range selected: 2025-07-09 to 2025-07-09
2025-07-09 14:02:35,423 - INFO - app - app.py:363 - No client selected, showing welcome message
2025-07-09 14:02:48,336 - INFO - app - app.py:82 - Starting application initialization
2025-07-09 14:02:48,339 - INFO - app - app.py:84 - Page configuration completed
2025-07-09 14:02:48,344 - INFO - app - app.py:88 - Session state initialized
2025-07-09 14:02:48,344 - INFO - app - app.py:92 - User not authenticated, showing login form
2025-07-09 14:02:51,924 - INFO - app - app.py:82 - Starting application initialization
2025-07-09 14:02:51,924 - INFO - app - app.py:84 - Page configuration completed
2025-07-09 14:02:51,924 - INFO - app - app.py:88 - Session state initialized
2025-07-09 14:02:51,924 - INFO - app - app.py:92 - User not authenticated, showing login form
2025-07-09 14:02:53,539 - INFO - app - app.py:82 - Starting application initialization
2025-07-09 14:02:53,556 - INFO - app - app.py:84 - Page configuration completed
2025-07-09 14:02:53,556 - INFO - app - app.py:88 - Session state initialized
2025-07-09 14:02:53,556 - INFO - app - app.py:96 - User authenticated successfully
2025-07-09 14:02:53,556 - INFO - app - app.py:108 - Loading client data from database
2025-07-09 14:02:53,647 - INFO - app - app.py:117 - Successfully loaded data for 1 clients
2025-07-09 14:02:53,648 - INFO - app - app.py:65 - Successfully loaded image: logo/logo_integrum.jpg
2025-07-09 14:02:53,651 - INFO - app - app.py:148 - Client selection: None, Plant: None, Type: None
2025-07-09 14:02:53,653 - INFO - app - app.py:159 - Date range selected: 2025-07-09 to 2025-07-09
2025-07-09 14:02:53,658 - INFO - app - app.py:363 - No client selected, showing welcome message
2025-07-09 14:02:55,693 - INFO - app - app.py:82 - Starting application initialization
2025-07-09 14:02:55,695 - INFO - app - app.py:84 - Page configuration completed
2025-07-09 14:02:55,695 - INFO - app - app.py:88 - Session state initialized
2025-07-09 14:02:55,695 - INFO - app - app.py:96 - User authenticated successfully
2025-07-09 14:02:55,695 - INFO - app - app.py:108 - Loading client data from database
2025-07-09 14:02:55,805 - INFO - app - app.py:117 - Successfully loaded data for 1 clients
2025-07-09 14:02:55,806 - INFO - app - app.py:65 - Successfully loaded image: logo/logo_integrum.jpg
2025-07-09 14:02:55,808 - INFO - app - app.py:148 - Client selection: Kids Clinic India Limited, Plant: None, Type: Combined
2025-07-09 14:02:55,810 - INFO - app - app.py:159 - Date range selected: 2025-07-09 to 2025-07-09
2025-07-09 14:02:55,814 - INFO - app - app.py:187 - Created main application tabs
2025-07-09 14:02:55,816 - INFO - app - app.py:197 - Summary tab: Displaying data for Kids Clinic India Limited
2025-07-09 14:02:56,826 - INFO - app - app.py:212 - Successfully displayed generation vs consumption chart for Kids Clinic India Limited
2025-07-09 14:02:58,852 - INFO - app - app.py:222 - Successfully displayed monthly banking settlement for Kids Clinic India Limited
2025-07-09 14:02:58,939 - INFO - app - app.py:233 - Successfully displayed ToD generation analysis for Kids Clinic India Limited
2025-07-09 14:02:59,022 - INFO - app - app.py:244 - Successfully displayed ToD consumption analysis for Kids Clinic India Limited
2025-07-09 14:02:59,123 - INFO - app - app.py:255 - Successfully displayed hourly generation vs consumption chart for Kids Clinic India Limited
2025-07-09 14:03:01,368 - INFO - app - app.py:277 - Successfully displayed unit-wise monthly bill analysis for Kids Clinic India Limited
2025-07-09 14:03:01,368 - INFO - app - app.py:289 - ToD Analysis tab: Displaying data for Kids Clinic India Limited
2025-07-09 14:03:01,738 - INFO - app - app.py:302 - Successfully displayed monthly ToD before banking for Kids Clinic India Limited
2025-07-09 14:03:01,840 - INFO - app - app.py:312 - Successfully displayed ToD generation vs consumption for Kids Clinic India Limited
2025-07-09 14:03:01,928 - INFO - app - app.py:323 - Successfully displayed ToD line chart for Kids Clinic India Limited
2025-07-09 14:03:02,430 - INFO - app - app.py:334 - Successfully displayed generation vs consumption settled heatmap for Kids Clinic India Limited
2025-07-09 14:03:02,432 - INFO - app - app.py:347 - Power Cost Analysis tab: Displaying data for Kids Clinic India Limited
2025-07-09 14:03:03,989 - INFO - app - app.py:352 - Successfully displayed power cost analysis for Kids Clinic India Limited
2025-07-09 14:04:09,237 - INFO - app - app.py:82 - Starting application initialization
2025-07-09 14:04:09,291 - INFO - app - app.py:84 - Page configuration completed
2025-07-09 14:04:09,292 - INFO - app - app.py:88 - Session state initialized
2025-07-09 14:04:09,293 - INFO - app - app.py:92 - User not authenticated, showing login form
2025-07-09 14:04:12,417 - INFO - app - app.py:82 - Starting application initialization
2025-07-09 14:04:12,459 - INFO - app - app.py:84 - Page configuration completed
2025-07-09 14:04:12,459 - INFO - app - app.py:88 - Session state initialized
2025-07-09 14:04:12,459 - INFO - app - app.py:92 - User not authenticated, showing login form
2025-07-09 14:04:14,066 - INFO - app - app.py:82 - Starting application initialization
2025-07-09 14:04:14,069 - INFO - app - app.py:84 - Page configuration completed
2025-07-09 14:04:14,069 - INFO - app - app.py:88 - Session state initialized
2025-07-09 14:04:14,069 - INFO - app - app.py:92 - User not authenticated, showing login form
2025-07-09 14:04:15,700 - INFO - app - app.py:82 - Starting application initialization
2025-07-09 14:04:15,700 - INFO - app - app.py:84 - Page configuration completed
2025-07-09 14:04:15,700 - INFO - app - app.py:88 - Session state initialized
2025-07-09 14:04:15,700 - INFO - app - app.py:96 - User authenticated successfully
2025-07-09 14:04:15,700 - INFO - app - app.py:108 - Loading client data from database
2025-07-09 14:04:16,073 - INFO - app - app.py:117 - Successfully loaded data for 1 clients
2025-07-09 14:04:16,073 - INFO - app - app.py:65 - Successfully loaded image: logo/logo_integrum.jpg
2025-07-09 14:04:16,073 - INFO - app - app.py:148 - Client selection: None, Plant: None, Type: None
2025-07-09 14:04:16,073 - INFO - app - app.py:159 - Date range selected: 2025-07-09 to 2025-07-09
2025-07-09 14:04:16,090 - INFO - app - app.py:363 - No client selected, showing welcome message
2025-07-09 14:04:17,936 - INFO - app - app.py:82 - Starting application initialization
2025-07-09 14:04:17,936 - INFO - app - app.py:84 - Page configuration completed
2025-07-09 14:04:17,936 - INFO - app - app.py:88 - Session state initialized
2025-07-09 14:04:17,936 - INFO - app - app.py:96 - User authenticated successfully
2025-07-09 14:04:17,936 - INFO - app - app.py:108 - Loading client data from database
2025-07-09 14:04:18,024 - INFO - app - app.py:117 - Successfully loaded data for 1 clients
2025-07-09 14:04:18,024 - INFO - app - app.py:65 - Successfully loaded image: logo/logo_integrum.jpg
2025-07-09 14:04:18,028 - INFO - app - app.py:148 - Client selection: Kids Clinic India Limited, Plant: None, Type: Combined
2025-07-09 14:04:18,029 - INFO - app - app.py:159 - Date range selected: 2025-07-09 to 2025-07-09
2025-07-09 14:04:18,032 - INFO - app - app.py:187 - Created main application tabs
2025-07-09 14:04:18,033 - INFO - app - app.py:197 - Summary tab: Displaying data for Kids Clinic India Limited
2025-07-09 14:04:19,317 - INFO - app - app.py:212 - Successfully displayed generation vs consumption chart for Kids Clinic India Limited
2025-07-09 14:04:21,069 - INFO - app - app.py:222 - Successfully displayed monthly banking settlement for Kids Clinic India Limited
2025-07-09 14:04:21,141 - INFO - app - app.py:233 - Successfully displayed ToD generation analysis for Kids Clinic India Limited
2025-07-09 14:04:21,231 - INFO - app - app.py:244 - Successfully displayed ToD consumption analysis for Kids Clinic India Limited
2025-07-09 14:04:21,351 - INFO - app - app.py:255 - Successfully displayed hourly generation vs consumption chart for Kids Clinic India Limited
2025-07-09 14:04:23,559 - INFO - app - app.py:277 - Successfully displayed unit-wise monthly bill analysis for Kids Clinic India Limited
2025-07-09 14:04:23,559 - INFO - app - app.py:289 - ToD Analysis tab: Displaying data for Kids Clinic India Limited
2025-07-09 14:04:24,195 - INFO - app - app.py:302 - Successfully displayed monthly ToD before banking for Kids Clinic India Limited
2025-07-09 14:04:24,297 - INFO - app - app.py:312 - Successfully displayed ToD generation vs consumption for Kids Clinic India Limited
2025-07-09 14:04:24,415 - INFO - app - app.py:323 - Successfully displayed ToD line chart for Kids Clinic India Limited
2025-07-09 14:04:25,184 - INFO - app - app.py:334 - Successfully displayed generation vs consumption settled heatmap for Kids Clinic India Limited
2025-07-09 14:04:25,184 - INFO - app - app.py:347 - Power Cost Analysis tab: Displaying data for Kids Clinic India Limited
2025-07-09 14:04:27,215 - INFO - app - app.py:352 - Successfully displayed power cost analysis for Kids Clinic India Limited
2025-07-09 14:04:35,782 - INFO - app - app.py:82 - Starting application initialization
2025-07-09 14:04:35,782 - INFO - app - app.py:84 - Page configuration completed
2025-07-09 14:04:35,782 - INFO - app - app.py:88 - Session state initialized
2025-07-09 14:04:35,782 - INFO - app - app.py:96 - User authenticated successfully
2025-07-09 14:04:35,782 - INFO - app - app.py:108 - Loading client data from database
2025-07-09 14:04:35,883 - INFO - app - app.py:117 - Successfully loaded data for 1 clients
2025-07-09 14:04:35,883 - INFO - app - app.py:65 - Successfully loaded image: logo/logo_integrum.jpg
2025-07-09 14:04:35,887 - INFO - app - app.py:148 - Client selection: Kids Clinic India Limited, Plant: None, Type: Combined
2025-07-09 14:04:35,891 - INFO - app - app.py:159 - Date range selected: 2025-05-01 to 2025-05-01
2025-07-09 14:04:35,901 - INFO - app - app.py:187 - Created main application tabs
2025-07-09 14:04:35,901 - INFO - app - app.py:197 - Summary tab: Displaying data for Kids Clinic India Limited
2025-07-09 14:04:36,684 - INFO - app - app.py:82 - Starting application initialization
2025-07-09 14:04:36,686 - INFO - app - app.py:84 - Page configuration completed
2025-07-09 14:04:36,686 - INFO - app - app.py:88 - Session state initialized
2025-07-09 14:04:36,686 - INFO - app - app.py:96 - User authenticated successfully
2025-07-09 14:04:36,686 - INFO - app - app.py:108 - Loading client data from database
2025-07-09 14:04:36,854 - INFO - app - app.py:117 - Successfully loaded data for 1 clients
2025-07-09 14:04:36,854 - INFO - app - app.py:65 - Successfully loaded image: logo/logo_integrum.jpg
2025-07-09 14:04:36,856 - INFO - app - app.py:148 - Client selection: Kids Clinic India Limited, Plant: None, Type: Combined
2025-07-09 14:04:36,859 - INFO - app - app.py:159 - Date range selected: 2025-05-01 to 2025-05-31
2025-07-09 14:04:36,863 - INFO - app - app.py:187 - Created main application tabs
2025-07-09 14:04:36,865 - INFO - app - app.py:197 - Summary tab: Displaying data for Kids Clinic India Limited
2025-07-09 14:04:37,747 - INFO - app - app.py:212 - Successfully displayed generation vs consumption chart for Kids Clinic India Limited
2025-07-09 14:04:38,689 - INFO - app - app.py:222 - Successfully displayed monthly banking settlement for Kids Clinic India Limited
2025-07-09 14:04:38,915 - INFO - app - app.py:233 - Successfully displayed ToD generation analysis for Kids Clinic India Limited
2025-07-09 14:04:39,159 - INFO - app - app.py:244 - Successfully displayed ToD consumption analysis for Kids Clinic India Limited
2025-07-09 14:04:39,824 - INFO - app - app.py:255 - Successfully displayed hourly generation vs consumption chart for Kids Clinic India Limited
2025-07-09 14:04:41,116 - INFO - app - app.py:277 - Successfully displayed unit-wise monthly bill analysis for Kids Clinic India Limited
2025-07-09 14:04:41,117 - INFO - app - app.py:289 - ToD Analysis tab: Displaying data for Kids Clinic India Limited
2025-07-09 14:04:41,468 - INFO - app - app.py:302 - Successfully displayed monthly ToD before banking for Kids Clinic India Limited
2025-07-09 14:04:41,969 - INFO - app - app.py:312 - Successfully displayed ToD generation vs consumption for Kids Clinic India Limited
2025-07-09 14:04:42,234 - INFO - app - app.py:323 - Successfully displayed ToD line chart for Kids Clinic India Limited
2025-07-09 14:04:42,664 - INFO - app - app.py:334 - Successfully displayed generation vs consumption settled heatmap for Kids Clinic India Limited
2025-07-09 14:04:42,666 - INFO - app - app.py:347 - Power Cost Analysis tab: Displaying data for Kids Clinic India Limited
2025-07-09 14:04:43,588 - INFO - app - app.py:352 - Successfully displayed power cost analysis for Kids Clinic India Limited
2025-07-09 14:04:56,179 - INFO - app - app.py:82 - Starting application initialization
2025-07-09 14:04:56,179 - INFO - app - app.py:84 - Page configuration completed
2025-07-09 14:04:56,179 - INFO - app - app.py:88 - Session state initialized
2025-07-09 14:04:56,179 - INFO - app - app.py:96 - User authenticated successfully
2025-07-09 14:04:56,189 - INFO - app - app.py:108 - Loading client data from database
2025-07-09 14:04:56,293 - INFO - app - app.py:117 - Successfully loaded data for 1 clients
2025-07-09 14:04:56,293 - INFO - app - app.py:65 - Successfully loaded image: logo/logo_integrum.jpg
2025-07-09 14:04:56,295 - INFO - app - app.py:148 - Client selection: Kids Clinic India Limited, Plant: None, Type: Combined
2025-07-09 14:04:56,296 - INFO - app - app.py:159 - Date range selected: 2025-05-01 to 2025-05-31
2025-07-09 14:04:56,301 - INFO - app - app.py:187 - Created main application tabs
2025-07-09 14:04:56,302 - INFO - app - app.py:197 - Summary tab: Displaying data for Kids Clinic India Limited
2025-07-09 14:04:57,340 - INFO - app - app.py:212 - Successfully displayed generation vs consumption chart for Kids Clinic India Limited
2025-07-09 14:04:58,283 - INFO - app - app.py:222 - Successfully displayed monthly banking settlement for Kids Clinic India Limited
2025-07-09 14:04:58,477 - INFO - app - app.py:233 - Successfully displayed ToD generation analysis for Kids Clinic India Limited
2025-07-09 14:04:58,703 - INFO - app - app.py:244 - Successfully displayed ToD consumption analysis for Kids Clinic India Limited
2025-07-09 14:04:58,935 - INFO - app - app.py:255 - Successfully displayed hourly generation vs consumption chart for Kids Clinic India Limited
2025-07-09 14:05:00,283 - INFO - app - app.py:277 - Successfully displayed unit-wise monthly bill analysis for Kids Clinic India Limited
2025-07-09 14:05:00,283 - INFO - app - app.py:289 - ToD Analysis tab: Displaying data for Kids Clinic India Limited
2025-07-09 14:05:00,665 - INFO - app - app.py:302 - Successfully displayed monthly ToD before banking for Kids Clinic India Limited
2025-07-09 14:05:01,207 - INFO - app - app.py:312 - Successfully displayed ToD generation vs consumption for Kids Clinic India Limited
2025-07-09 14:05:01,453 - INFO - app - app.py:323 - Successfully displayed ToD line chart for Kids Clinic India Limited
2025-07-09 14:05:01,940 - INFO - app - app.py:334 - Successfully displayed generation vs consumption settled heatmap for Kids Clinic India Limited
2025-07-09 14:05:01,941 - INFO - app - app.py:347 - Power Cost Analysis tab: Displaying data for Kids Clinic India Limited
2025-07-09 14:05:03,136 - INFO - app - app.py:352 - Successfully displayed power cost analysis for Kids Clinic India Limited
2025-07-09 14:05:10,557 - INFO - app - app.py:82 - Starting application initialization
2025-07-09 14:05:10,557 - INFO - app - app.py:84 - Page configuration completed
2025-07-09 14:05:10,557 - INFO - app - app.py:88 - Session state initialized
2025-07-09 14:05:10,557 - INFO - app - app.py:96 - User authenticated successfully
2025-07-09 14:05:10,557 - INFO - app - app.py:108 - Loading client data from database
2025-07-09 14:05:10,680 - INFO - app - app.py:117 - Successfully loaded data for 1 clients
2025-07-09 14:05:10,680 - INFO - app - app.py:65 - Successfully loaded image: logo/logo_integrum.jpg
2025-07-09 14:05:10,683 - INFO - app - app.py:148 - Client selection: Kids Clinic India Limited, Plant: None, Type: Combined
2025-07-09 14:05:10,683 - INFO - app - app.py:159 - Date range selected: 2025-05-01 to 2025-05-31
2025-07-09 14:05:10,686 - INFO - app - app.py:187 - Created main application tabs
2025-07-09 14:05:10,687 - INFO - app - app.py:197 - Summary tab: Displaying data for Kids Clinic India Limited
2025-07-09 14:05:11,512 - INFO - app - app.py:212 - Successfully displayed generation vs consumption chart for Kids Clinic India Limited
2025-07-09 14:05:12,501 - INFO - app - app.py:222 - Successfully displayed monthly banking settlement for Kids Clinic India Limited
2025-07-09 14:05:12,734 - INFO - app - app.py:233 - Successfully displayed ToD generation analysis for Kids Clinic India Limited
2025-07-09 14:05:12,949 - INFO - app - app.py:244 - Successfully displayed ToD consumption analysis for Kids Clinic India Limited
2025-07-09 14:05:13,201 - INFO - app - app.py:255 - Successfully displayed hourly generation vs consumption chart for Kids Clinic India Limited
2025-07-09 14:05:14,423 - INFO - app - app.py:277 - Successfully displayed unit-wise monthly bill analysis for Kids Clinic India Limited
2025-07-09 14:05:14,423 - INFO - app - app.py:289 - ToD Analysis tab: Displaying data for Kids Clinic India Limited
2025-07-09 14:05:14,734 - INFO - app - app.py:302 - Successfully displayed monthly ToD before banking for Kids Clinic India Limited
2025-07-09 14:05:15,183 - INFO - app - app.py:312 - Successfully displayed ToD generation vs consumption for Kids Clinic India Limited
2025-07-09 14:05:15,405 - INFO - app - app.py:323 - Successfully displayed ToD line chart for Kids Clinic India Limited
2025-07-09 14:05:15,847 - INFO - app - app.py:334 - Successfully displayed generation vs consumption settled heatmap for Kids Clinic India Limited
2025-07-09 14:05:15,849 - INFO - app - app.py:347 - Power Cost Analysis tab: Displaying data for Kids Clinic India Limited
2025-07-09 14:05:16,805 - INFO - app - app.py:352 - Successfully displayed power cost analysis for Kids Clinic India Limited
2025-07-09 14:27:29,801 - INFO - app - app.py:82 - Starting application initialization
2025-07-09 14:27:29,803 - INFO - app - app.py:84 - Page configuration completed
2025-07-09 14:27:29,803 - INFO - app - app.py:88 - Session state initialized
2025-07-09 14:27:29,804 - INFO - app - app.py:96 - User authenticated successfully
2025-07-09 14:27:29,806 - INFO - app - app.py:108 - Loading client data from database
2025-07-09 14:27:29,887 - INFO - app - app.py:117 - Successfully loaded data for 1 clients
2025-07-09 14:27:29,887 - INFO - app - app.py:65 - Successfully loaded image: logo/logo_integrum.jpg
2025-07-09 14:27:29,889 - INFO - app - app.py:148 - Client selection: Kids Clinic India Limited, Plant: None, Type: Combined
2025-07-09 14:27:29,890 - INFO - app - app.py:159 - Date range selected: 2025-05-01 to 2025-05-31
2025-07-09 14:27:29,895 - INFO - app - app.py:187 - Created main application tabs
2025-07-09 14:27:29,895 - INFO - app - app.py:197 - Summary tab: Displaying data for Kids Clinic India Limited
2025-07-09 14:27:30,716 - INFO - app - app.py:212 - Successfully displayed generation vs consumption chart for Kids Clinic India Limited
2025-07-09 14:27:31,690 - INFO - app - app.py:222 - Successfully displayed monthly banking settlement for Kids Clinic India Limited
2025-07-09 14:27:31,921 - INFO - app - app.py:233 - Successfully displayed ToD generation analysis for Kids Clinic India Limited
2025-07-09 14:27:32,132 - INFO - app - app.py:244 - Successfully displayed ToD consumption analysis for Kids Clinic India Limited
2025-07-09 14:27:32,364 - INFO - app - app.py:255 - Successfully displayed hourly generation vs consumption chart for Kids Clinic India Limited
2025-07-09 14:27:34,274 - INFO - app - app.py:277 - Successfully displayed unit-wise monthly bill analysis for Kids Clinic India Limited
2025-07-09 14:27:34,274 - INFO - app - app.py:289 - ToD Analysis tab: Displaying data for Kids Clinic India Limited
2025-07-09 14:27:34,691 - INFO - app - app.py:302 - Successfully displayed monthly ToD before banking for Kids Clinic India Limited
2025-07-09 14:27:35,444 - INFO - app - app.py:312 - Successfully displayed ToD generation vs consumption for Kids Clinic India Limited
2025-07-09 14:27:35,701 - INFO - app - app.py:323 - Successfully displayed ToD line chart for Kids Clinic India Limited
2025-07-09 14:27:36,142 - INFO - app - app.py:334 - Successfully displayed generation vs consumption settled heatmap for Kids Clinic India Limited
2025-07-09 14:27:36,143 - INFO - app - app.py:347 - Power Cost Analysis tab: Displaying data for Kids Clinic India Limited
2025-07-09 14:27:37,709 - INFO - app - app.py:352 - Successfully displayed power cost analysis for Kids Clinic India Limited
2025-07-09 14:28:27,888 - INFO - app - app.py:82 - Starting application initialization
2025-07-09 14:28:27,888 - INFO - app - app.py:84 - Page configuration completed
2025-07-09 14:28:27,888 - INFO - app - app.py:88 - Session state initialized
2025-07-09 14:28:27,900 - INFO - app - app.py:96 - User authenticated successfully
2025-07-09 14:28:27,900 - INFO - app - app.py:108 - Loading client data from database
2025-07-09 14:28:28,003 - INFO - app - app.py:117 - Successfully loaded data for 1 clients
2025-07-09 14:28:28,003 - INFO - app - app.py:65 - Successfully loaded image: logo/logo_integrum.jpg
2025-07-09 14:28:28,003 - INFO - app - app.py:148 - Client selection: Kids Clinic India Limited, Plant: None, Type: Combined
2025-07-09 14:28:28,013 - INFO - app - app.py:159 - Date range selected: 2025-05-01 to 2025-05-31
2025-07-09 14:28:28,013 - INFO - app - app.py:187 - Created main application tabs
2025-07-09 14:28:28,013 - INFO - app - app.py:197 - Summary tab: Displaying data for Kids Clinic India Limited
2025-07-09 14:28:28,968 - INFO - app - app.py:212 - Successfully displayed generation vs consumption chart for Kids Clinic India Limited
2025-07-09 14:28:30,737 - INFO - app - app.py:222 - Successfully displayed monthly banking settlement for Kids Clinic India Limited
2025-07-09 14:28:30,953 - INFO - app - app.py:233 - Successfully displayed ToD generation analysis for Kids Clinic India Limited
2025-07-09 14:28:31,270 - INFO - app - app.py:244 - Successfully displayed ToD consumption analysis for Kids Clinic India Limited
2025-07-09 14:28:31,588 - INFO - app - app.py:255 - Successfully displayed hourly generation vs consumption chart for Kids Clinic India Limited
2025-07-09 14:28:33,649 - INFO - app - app.py:277 - Successfully displayed unit-wise monthly bill analysis for Kids Clinic India Limited
2025-07-09 14:28:33,649 - INFO - app - app.py:289 - ToD Analysis tab: Displaying data for Kids Clinic India Limited
2025-07-09 14:28:34,053 - INFO - app - app.py:302 - Successfully displayed monthly ToD before banking for Kids Clinic India Limited
2025-07-09 14:28:34,787 - INFO - app - app.py:312 - Successfully displayed ToD generation vs consumption for Kids Clinic India Limited
2025-07-09 14:28:35,029 - INFO - app - app.py:323 - Successfully displayed ToD line chart for Kids Clinic India Limited
2025-07-09 14:28:35,520 - INFO - app - app.py:334 - Successfully displayed generation vs consumption settled heatmap for Kids Clinic India Limited
2025-07-09 14:28:35,520 - INFO - app - app.py:347 - Power Cost Analysis tab: Displaying data for Kids Clinic India Limited
2025-07-09 14:28:36,615 - INFO - app - app.py:82 - Starting application initialization
2025-07-09 14:28:36,615 - INFO - app - app.py:84 - Page configuration completed
2025-07-09 14:28:36,615 - INFO - app - app.py:88 - Session state initialized
2025-07-09 14:28:36,615 - INFO - app - app.py:96 - User authenticated successfully
2025-07-09 14:28:36,621 - INFO - app - app.py:108 - Loading client data from database
2025-07-09 14:28:37,051 - INFO - app - app.py:117 - Successfully loaded data for 1 clients
2025-07-09 14:28:37,071 - INFO - app - app.py:65 - Successfully loaded image: logo/logo_integrum.jpg
2025-07-09 14:28:37,115 - INFO - app - app.py:148 - Client selection: Kids Clinic India Limited, Plant: None, Type: Combined
2025-07-09 14:28:37,237 - INFO - app - app.py:159 - Date range selected: 2025-05-01 to 2025-05-31
2025-07-09 14:28:37,255 - INFO - app - app.py:187 - Created main application tabs
2025-07-09 14:28:37,255 - INFO - app - app.py:197 - Summary tab: Displaying data for Kids Clinic India Limited
2025-07-09 14:28:38,156 - INFO - app - app.py:212 - Successfully displayed generation vs consumption chart for Kids Clinic India Limited
2025-07-09 14:28:40,006 - INFO - app - app.py:222 - Successfully displayed monthly banking settlement for Kids Clinic India Limited
2025-07-09 14:28:40,214 - INFO - app - app.py:233 - Successfully displayed ToD generation analysis for Kids Clinic India Limited
2025-07-09 14:28:40,448 - INFO - app - app.py:244 - Successfully displayed ToD consumption analysis for Kids Clinic India Limited
2025-07-09 14:28:40,734 - INFO - app - app.py:255 - Successfully displayed hourly generation vs consumption chart for Kids Clinic India Limited
2025-07-09 14:28:42,803 - INFO - app - app.py:277 - Successfully displayed unit-wise monthly bill analysis for Kids Clinic India Limited
2025-07-09 14:28:42,803 - INFO - app - app.py:289 - ToD Analysis tab: Displaying data for Kids Clinic India Limited
2025-07-09 14:28:43,389 - INFO - app - app.py:302 - Successfully displayed monthly ToD before banking for Kids Clinic India Limited
2025-07-09 14:28:44,236 - INFO - app - app.py:312 - Successfully displayed ToD generation vs consumption for Kids Clinic India Limited
2025-07-09 14:28:44,553 - INFO - app - app.py:323 - Successfully displayed ToD line chart for Kids Clinic India Limited
2025-07-09 14:28:45,007 - INFO - app - app.py:334 - Successfully displayed generation vs consumption settled heatmap for Kids Clinic India Limited
2025-07-09 14:28:45,007 - INFO - app - app.py:347 - Power Cost Analysis tab: Displaying data for Kids Clinic India Limited
2025-07-09 14:28:46,812 - INFO - app - app.py:352 - Successfully displayed power cost analysis for Kids Clinic India Limited
2025-07-09 14:49:38,603 - INFO - app - app.py:82 - Starting application initialization
2025-07-09 14:49:38,603 - INFO - app - app.py:84 - Page configuration completed
2025-07-09 14:49:38,603 - INFO - app - app.py:88 - Session state initialized
2025-07-09 14:49:38,603 - INFO - app - app.py:96 - User authenticated successfully
2025-07-09 14:49:38,603 - INFO - app - app.py:108 - Loading client data from database
2025-07-09 14:49:38,697 - INFO - app - app.py:117 - Successfully loaded data for 1 clients
2025-07-09 14:49:38,697 - INFO - app - app.py:65 - Successfully loaded image: logo/logo_integrum.jpg
2025-07-09 14:49:38,699 - INFO - app - app.py:148 - Client selection: Kids Clinic India Limited, Plant: None, Type: Combined
2025-07-09 14:49:38,701 - INFO - app - app.py:159 - Date range selected: 2025-05-01 to 2025-05-31
2025-07-09 14:49:38,704 - INFO - app - app.py:187 - Created main application tabs
2025-07-09 14:49:38,705 - INFO - app - app.py:197 - Summary tab: Displaying data for Kids Clinic India Limited
2025-07-09 14:49:39,664 - INFO - app - app.py:212 - Successfully displayed generation vs consumption chart for Kids Clinic India Limited
2025-07-09 14:49:41,493 - INFO - app - app.py:222 - Successfully displayed monthly banking settlement for Kids Clinic India Limited
2025-07-09 14:49:41,710 - INFO - app - app.py:233 - Successfully displayed ToD generation analysis for Kids Clinic India Limited
2025-07-09 14:49:41,942 - INFO - app - app.py:244 - Successfully displayed ToD consumption analysis for Kids Clinic India Limited
2025-07-09 14:49:42,234 - INFO - app - app.py:255 - Successfully displayed hourly generation vs consumption chart for Kids Clinic India Limited
2025-07-09 14:49:44,495 - INFO - app - app.py:277 - Successfully displayed unit-wise monthly bill analysis for Kids Clinic India Limited
2025-07-09 14:49:44,495 - INFO - app - app.py:289 - ToD Analysis tab: Displaying data for Kids Clinic India Limited
2025-07-09 14:49:45,007 - INFO - app - app.py:302 - Successfully displayed monthly ToD before banking for Kids Clinic India Limited
2025-07-09 14:49:45,726 - INFO - app - app.py:312 - Successfully displayed ToD generation vs consumption for Kids Clinic India Limited
2025-07-09 14:49:45,975 - INFO - app - app.py:323 - Successfully displayed ToD line chart for Kids Clinic India Limited
2025-07-09 14:49:46,438 - INFO - app - app.py:334 - Successfully displayed generation vs consumption settled heatmap for Kids Clinic India Limited
2025-07-09 14:49:46,439 - INFO - app - app.py:347 - Power Cost Analysis tab: Displaying data for Kids Clinic India Limited
2025-07-09 14:49:48,154 - INFO - app - app.py:352 - Successfully displayed power cost analysis for Kids Clinic India Limited
