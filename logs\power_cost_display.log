2025-07-08 12:02:40,382 - INFO - power_cost_display - power_cost_display.py:27 - Starting power cost analysis for plant: Kids Clinic India Limited
2025-07-08 12:02:40,382 - INFO - power_cost_display - power_cost_display.py:32 - Interactive plot setting: True
2025-07-08 12:02:40,382 - INFO - power_cost_display - power_cost_display.py:57 - Banking option selected: Without Banking
2025-07-08 12:02:40,382 - INFO - power_cost_display - power_cost_display.py:81 - Grid rate set to: 4.0
2025-07-08 12:02:40,390 - INFO - power_cost_display - power_cost_display.py:96 - Fetching combined monthly data for plant: Kids Clinic India Limited
2025-07-08 12:02:41,577 - INFO - power_cost_display - power_cost_display.py:104 - Successfully fetched data with 6 records
2025-07-08 12:02:41,632 - INFO - power_cost_display - power_cost_display.py:222 - Processing 'Without Banking' analysis
2025-07-08 12:02:41,632 - INFO - power_cost_display - power_cost_display.py:225 - Calculating monthly power costs without banking
2025-07-08 12:02:41,649 - INFO - power_cost_display - power_cost_display.py:233 - Successfully calculated power costs without banking, 6 records
2025-07-08 12:02:41,649 - INFO - power_cost_display - power_cost_display.py:238 - Successfully generated summary table for without banking analysis
2025-07-08 12:02:41,657 - INFO - power_cost_display - power_cost_display.py:279 - Successfully displayed without banking analysis metrics
2025-07-08 12:02:41,682 - INFO - power_cost_display - power_cost_display.py:290 - Successfully displayed interactive without banking cost chart
2025-07-08 12:02:41,682 - INFO - power_cost_display - power_cost_display.py:321 - Successfully displayed detailed without banking cost table
2025-07-08 12:02:53,766 - INFO - power_cost_display - power_cost_display.py:27 - Starting power cost analysis for plant: Kids Clinic India Limited
2025-07-08 12:02:53,766 - INFO - power_cost_display - power_cost_display.py:32 - Interactive plot setting: True
2025-07-08 12:02:53,770 - INFO - power_cost_display - power_cost_display.py:57 - Banking option selected: Without Banking
2025-07-08 12:02:53,770 - INFO - power_cost_display - power_cost_display.py:81 - Grid rate set to: 4.0
2025-07-08 12:02:53,772 - INFO - power_cost_display - power_cost_display.py:96 - Fetching combined monthly data for plant: Kids Clinic India Limited
2025-07-08 12:02:55,010 - INFO - power_cost_display - power_cost_display.py:104 - Successfully fetched data with 6 records
2025-07-08 12:02:55,011 - INFO - power_cost_display - power_cost_display.py:222 - Processing 'Without Banking' analysis
2025-07-08 12:02:55,011 - INFO - power_cost_display - power_cost_display.py:225 - Calculating monthly power costs without banking
2025-07-08 12:02:55,014 - INFO - power_cost_display - power_cost_display.py:233 - Successfully calculated power costs without banking, 6 records
2025-07-08 12:02:55,017 - INFO - power_cost_display - power_cost_display.py:238 - Successfully generated summary table for without banking analysis
2025-07-08 12:02:55,018 - INFO - power_cost_display - power_cost_display.py:279 - Successfully displayed without banking analysis metrics
2025-07-08 12:02:55,042 - INFO - power_cost_display - power_cost_display.py:290 - Successfully displayed interactive without banking cost chart
2025-07-08 12:02:55,042 - INFO - power_cost_display - power_cost_display.py:321 - Successfully displayed detailed without banking cost table
2025-07-08 12:28:18,135 - INFO - power_cost_display - power_cost_display.py:27 - Starting power cost analysis for plant: Kids Clinic India Limited
2025-07-08 12:28:18,137 - INFO - power_cost_display - power_cost_display.py:32 - Interactive plot setting: True
2025-07-08 12:28:18,139 - INFO - power_cost_display - power_cost_display.py:57 - Banking option selected: Without Banking
2025-07-08 12:28:18,139 - INFO - power_cost_display - power_cost_display.py:81 - Grid rate set to: 4.0
2025-07-08 12:28:18,139 - INFO - power_cost_display - power_cost_display.py:96 - Fetching combined monthly data for plant: Kids Clinic India Limited
2025-07-08 12:28:19,826 - INFO - power_cost_display - power_cost_display.py:104 - Successfully fetched data with 6 records
2025-07-08 12:28:19,826 - INFO - power_cost_display - power_cost_display.py:222 - Processing 'Without Banking' analysis
2025-07-08 12:28:19,826 - INFO - power_cost_display - power_cost_display.py:225 - Calculating monthly power costs without banking
2025-07-08 12:28:19,826 - INFO - power_cost_display - power_cost_display.py:233 - Successfully calculated power costs without banking, 6 records
2025-07-08 12:28:19,826 - INFO - power_cost_display - power_cost_display.py:238 - Successfully generated summary table for without banking analysis
2025-07-08 12:28:19,826 - INFO - power_cost_display - power_cost_display.py:279 - Successfully displayed without banking analysis metrics
2025-07-08 12:28:19,861 - INFO - power_cost_display - power_cost_display.py:290 - Successfully displayed interactive without banking cost chart
2025-07-08 12:28:19,863 - INFO - power_cost_display - power_cost_display.py:321 - Successfully displayed detailed without banking cost table
2025-07-08 12:28:30,116 - INFO - power_cost_display - power_cost_display.py:27 - Starting power cost analysis for plant: Kids Clinic India Limited
2025-07-08 12:28:30,117 - INFO - power_cost_display - power_cost_display.py:32 - Interactive plot setting: True
2025-07-08 12:28:30,118 - INFO - power_cost_display - power_cost_display.py:57 - Banking option selected: Without Banking
2025-07-08 12:28:30,120 - INFO - power_cost_display - power_cost_display.py:81 - Grid rate set to: 4.0
2025-07-08 12:28:30,120 - INFO - power_cost_display - power_cost_display.py:96 - Fetching combined monthly data for plant: Kids Clinic India Limited
2025-07-08 12:28:31,300 - INFO - power_cost_display - power_cost_display.py:104 - Successfully fetched data with 6 records
2025-07-08 12:28:31,301 - INFO - power_cost_display - power_cost_display.py:222 - Processing 'Without Banking' analysis
2025-07-08 12:28:31,301 - INFO - power_cost_display - power_cost_display.py:225 - Calculating monthly power costs without banking
2025-07-08 12:28:31,303 - INFO - power_cost_display - power_cost_display.py:233 - Successfully calculated power costs without banking, 6 records
2025-07-08 12:28:31,304 - INFO - power_cost_display - power_cost_display.py:238 - Successfully generated summary table for without banking analysis
2025-07-08 12:28:31,307 - INFO - power_cost_display - power_cost_display.py:279 - Successfully displayed without banking analysis metrics
2025-07-08 12:28:31,325 - INFO - power_cost_display - power_cost_display.py:290 - Successfully displayed interactive without banking cost chart
2025-07-08 12:28:31,328 - INFO - power_cost_display - power_cost_display.py:321 - Successfully displayed detailed without banking cost table
2025-07-08 14:54:08,823 - INFO - power_cost_display - power_cost_display.py:27 - Starting power cost analysis for plant: Kids Clinic India Limited
2025-07-08 14:54:08,823 - INFO - power_cost_display - power_cost_display.py:32 - Interactive plot setting: True
2025-07-08 14:54:08,824 - INFO - power_cost_display - power_cost_display.py:57 - Banking option selected: Without Banking
2025-07-08 14:54:08,826 - INFO - power_cost_display - power_cost_display.py:81 - Grid rate set to: 4.0
2025-07-08 14:54:08,827 - INFO - power_cost_display - power_cost_display.py:96 - Fetching combined monthly data for plant: Kids Clinic India Limited
2025-07-08 14:54:10,313 - INFO - power_cost_display - power_cost_display.py:104 - Successfully fetched data with 6 records
2025-07-08 14:54:10,313 - INFO - power_cost_display - power_cost_display.py:222 - Processing 'Without Banking' analysis
2025-07-08 14:54:10,313 - INFO - power_cost_display - power_cost_display.py:225 - Calculating monthly power costs without banking
2025-07-08 14:54:10,326 - INFO - power_cost_display - power_cost_display.py:233 - Successfully calculated power costs without banking, 6 records
2025-07-08 14:54:10,329 - INFO - power_cost_display - power_cost_display.py:238 - Successfully generated summary table for without banking analysis
2025-07-08 14:54:10,329 - INFO - power_cost_display - power_cost_display.py:279 - Successfully displayed without banking analysis metrics
2025-07-08 14:54:10,363 - INFO - power_cost_display - power_cost_display.py:290 - Successfully displayed interactive without banking cost chart
2025-07-08 14:54:10,367 - INFO - power_cost_display - power_cost_display.py:321 - Successfully displayed detailed without banking cost table
2025-07-08 14:54:17,665 - INFO - power_cost_display - power_cost_display.py:27 - Starting power cost analysis for plant: Kids Clinic India Limited
2025-07-08 14:54:17,665 - INFO - power_cost_display - power_cost_display.py:32 - Interactive plot setting: True
2025-07-08 14:54:17,665 - INFO - power_cost_display - power_cost_display.py:57 - Banking option selected: Without Banking
2025-07-08 14:54:17,665 - INFO - power_cost_display - power_cost_display.py:81 - Grid rate set to: 4.0
2025-07-08 14:54:17,665 - INFO - power_cost_display - power_cost_display.py:96 - Fetching combined monthly data for plant: Kids Clinic India Limited
2025-07-08 14:54:19,249 - INFO - power_cost_display - power_cost_display.py:104 - Successfully fetched data with 6 records
2025-07-08 14:54:19,249 - INFO - power_cost_display - power_cost_display.py:222 - Processing 'Without Banking' analysis
2025-07-08 14:54:19,249 - INFO - power_cost_display - power_cost_display.py:225 - Calculating monthly power costs without banking
2025-07-08 14:54:19,265 - INFO - power_cost_display - power_cost_display.py:233 - Successfully calculated power costs without banking, 6 records
2025-07-08 14:54:19,265 - INFO - power_cost_display - power_cost_display.py:238 - Successfully generated summary table for without banking analysis
2025-07-08 14:54:19,265 - INFO - power_cost_display - power_cost_display.py:279 - Successfully displayed without banking analysis metrics
2025-07-08 14:54:19,300 - INFO - power_cost_display - power_cost_display.py:290 - Successfully displayed interactive without banking cost chart
2025-07-08 14:54:19,300 - INFO - power_cost_display - power_cost_display.py:321 - Successfully displayed detailed without banking cost table
2025-07-08 15:48:21,038 - INFO - power_cost_display - power_cost_display.py:27 - Starting power cost analysis for plant: Kids Clinic India Limited
2025-07-08 15:48:21,038 - INFO - power_cost_display - power_cost_display.py:32 - Interactive plot setting: True
2025-07-08 15:48:21,038 - INFO - power_cost_display - power_cost_display.py:57 - Banking option selected: Without Banking
2025-07-08 15:48:21,038 - INFO - power_cost_display - power_cost_display.py:81 - Grid rate set to: 4.0
2025-07-08 15:48:21,038 - INFO - power_cost_display - power_cost_display.py:96 - Fetching combined monthly data for plant: Kids Clinic India Limited
2025-07-08 15:48:22,639 - INFO - power_cost_display - power_cost_display.py:104 - Successfully fetched data with 6 records
2025-07-08 15:48:22,639 - INFO - power_cost_display - power_cost_display.py:222 - Processing 'Without Banking' analysis
2025-07-08 15:48:22,639 - INFO - power_cost_display - power_cost_display.py:225 - Calculating monthly power costs without banking
2025-07-08 15:48:22,639 - INFO - power_cost_display - power_cost_display.py:233 - Successfully calculated power costs without banking, 6 records
2025-07-08 15:48:22,639 - INFO - power_cost_display - power_cost_display.py:238 - Successfully generated summary table for without banking analysis
2025-07-08 15:48:22,654 - INFO - power_cost_display - power_cost_display.py:279 - Successfully displayed without banking analysis metrics
2025-07-08 15:48:22,677 - INFO - power_cost_display - power_cost_display.py:290 - Successfully displayed interactive without banking cost chart
2025-07-08 15:48:22,677 - INFO - power_cost_display - power_cost_display.py:321 - Successfully displayed detailed without banking cost table
2025-07-08 15:51:00,806 - INFO - power_cost_display - power_cost_display.py:27 - Starting power cost analysis for plant: Kids Clinic India Limited
2025-07-08 15:51:00,808 - INFO - power_cost_display - power_cost_display.py:32 - Interactive plot setting: True
2025-07-08 15:51:00,810 - INFO - power_cost_display - power_cost_display.py:57 - Banking option selected: Without Banking
2025-07-08 15:51:00,811 - INFO - power_cost_display - power_cost_display.py:81 - Grid rate set to: 4.0
2025-07-08 15:51:00,813 - INFO - power_cost_display - power_cost_display.py:96 - Fetching combined monthly data for plant: Kids Clinic India Limited
2025-07-08 15:51:01,971 - INFO - power_cost_display - power_cost_display.py:104 - Successfully fetched data with 6 records
2025-07-08 15:51:01,971 - INFO - power_cost_display - power_cost_display.py:222 - Processing 'Without Banking' analysis
2025-07-08 15:51:01,971 - INFO - power_cost_display - power_cost_display.py:225 - Calculating monthly power costs without banking
2025-07-08 15:51:01,974 - INFO - power_cost_display - power_cost_display.py:233 - Successfully calculated power costs without banking, 6 records
2025-07-08 15:51:01,975 - INFO - power_cost_display - power_cost_display.py:238 - Successfully generated summary table for without banking analysis
2025-07-08 15:51:01,976 - INFO - power_cost_display - power_cost_display.py:279 - Successfully displayed without banking analysis metrics
2025-07-08 15:51:02,008 - INFO - power_cost_display - power_cost_display.py:290 - Successfully displayed interactive without banking cost chart
2025-07-08 15:51:02,011 - INFO - power_cost_display - power_cost_display.py:321 - Successfully displayed detailed without banking cost table
2025-07-08 15:51:18,234 - INFO - power_cost_display - power_cost_display.py:27 - Starting power cost analysis for plant: Kids Clinic India Limited
2025-07-08 15:51:18,236 - INFO - power_cost_display - power_cost_display.py:32 - Interactive plot setting: True
2025-07-08 15:51:18,236 - INFO - power_cost_display - power_cost_display.py:57 - Banking option selected: Without Banking
2025-07-08 15:51:18,239 - INFO - power_cost_display - power_cost_display.py:81 - Grid rate set to: 4.0
2025-07-08 15:51:18,240 - INFO - power_cost_display - power_cost_display.py:96 - Fetching combined monthly data for plant: Kids Clinic India Limited
2025-07-08 15:51:19,140 - INFO - power_cost_display - power_cost_display.py:104 - Successfully fetched data with 6 records
2025-07-08 15:51:19,140 - INFO - power_cost_display - power_cost_display.py:222 - Processing 'Without Banking' analysis
2025-07-08 15:51:19,140 - INFO - power_cost_display - power_cost_display.py:225 - Calculating monthly power costs without banking
2025-07-08 15:51:19,152 - INFO - power_cost_display - power_cost_display.py:233 - Successfully calculated power costs without banking, 6 records
2025-07-08 15:51:19,152 - INFO - power_cost_display - power_cost_display.py:238 - Successfully generated summary table for without banking analysis
2025-07-08 15:51:19,156 - INFO - power_cost_display - power_cost_display.py:279 - Successfully displayed without banking analysis metrics
2025-07-08 15:51:19,172 - INFO - power_cost_display - power_cost_display.py:290 - Successfully displayed interactive without banking cost chart
2025-07-08 15:51:19,176 - INFO - power_cost_display - power_cost_display.py:321 - Successfully displayed detailed without banking cost table
2025-07-08 15:53:09,195 - INFO - power_cost_display - power_cost_display.py:27 - Starting power cost analysis for plant: Kids Clinic India Limited
2025-07-08 15:53:09,195 - INFO - power_cost_display - power_cost_display.py:32 - Interactive plot setting: True
2025-07-08 15:53:09,195 - INFO - power_cost_display - power_cost_display.py:57 - Banking option selected: Without Banking
2025-07-08 15:53:09,195 - INFO - power_cost_display - power_cost_display.py:81 - Grid rate set to: 4.0
2025-07-08 15:53:09,195 - INFO - power_cost_display - power_cost_display.py:96 - Fetching combined monthly data for plant: Kids Clinic India Limited
2025-07-08 15:53:10,143 - INFO - power_cost_display - power_cost_display.py:104 - Successfully fetched data with 6 records
2025-07-08 15:53:10,143 - INFO - power_cost_display - power_cost_display.py:222 - Processing 'Without Banking' analysis
2025-07-08 15:53:10,143 - INFO - power_cost_display - power_cost_display.py:225 - Calculating monthly power costs without banking
2025-07-08 15:53:10,158 - INFO - power_cost_display - power_cost_display.py:233 - Successfully calculated power costs without banking, 6 records
2025-07-08 15:53:10,159 - INFO - power_cost_display - power_cost_display.py:238 - Successfully generated summary table for without banking analysis
2025-07-08 15:53:10,162 - INFO - power_cost_display - power_cost_display.py:279 - Successfully displayed without banking analysis metrics
2025-07-08 15:53:10,180 - INFO - power_cost_display - power_cost_display.py:290 - Successfully displayed interactive without banking cost chart
2025-07-08 15:53:10,184 - INFO - power_cost_display - power_cost_display.py:321 - Successfully displayed detailed without banking cost table
2025-07-08 16:03:11,943 - INFO - power_cost_display - power_cost_display.py:27 - Starting power cost analysis for plant: Kids Clinic India Limited
2025-07-08 16:03:11,943 - INFO - power_cost_display - power_cost_display.py:32 - Interactive plot setting: True
2025-07-08 16:03:11,943 - INFO - power_cost_display - power_cost_display.py:57 - Banking option selected: Without Banking
2025-07-08 16:03:11,943 - INFO - power_cost_display - power_cost_display.py:81 - Grid rate set to: 4.0
2025-07-08 16:03:11,943 - INFO - power_cost_display - power_cost_display.py:96 - Fetching combined monthly data for plant: Kids Clinic India Limited
2025-07-08 16:03:12,887 - INFO - power_cost_display - power_cost_display.py:104 - Successfully fetched data with 6 records
2025-07-08 16:03:12,903 - INFO - power_cost_display - power_cost_display.py:222 - Processing 'Without Banking' analysis
2025-07-08 16:03:12,903 - INFO - power_cost_display - power_cost_display.py:225 - Calculating monthly power costs without banking
2025-07-08 16:03:12,905 - INFO - power_cost_display - power_cost_display.py:233 - Successfully calculated power costs without banking, 6 records
2025-07-08 16:03:12,906 - INFO - power_cost_display - power_cost_display.py:238 - Successfully generated summary table for without banking analysis
2025-07-08 16:03:12,908 - INFO - power_cost_display - power_cost_display.py:279 - Successfully displayed without banking analysis metrics
2025-07-08 16:03:12,925 - INFO - power_cost_display - power_cost_display.py:290 - Successfully displayed interactive without banking cost chart
2025-07-08 16:03:12,928 - INFO - power_cost_display - power_cost_display.py:321 - Successfully displayed detailed without banking cost table
2025-07-08 16:04:54,290 - INFO - power_cost_display - power_cost_display.py:27 - Starting power cost analysis for plant: Kids Clinic India Limited
2025-07-08 16:04:54,291 - INFO - power_cost_display - power_cost_display.py:32 - Interactive plot setting: True
2025-07-08 16:04:54,292 - INFO - power_cost_display - power_cost_display.py:57 - Banking option selected: Without Banking
2025-07-08 16:04:54,293 - INFO - power_cost_display - power_cost_display.py:81 - Grid rate set to: 4.0
2025-07-08 16:04:54,294 - INFO - power_cost_display - power_cost_display.py:96 - Fetching combined monthly data for plant: Kids Clinic India Limited
2025-07-08 16:04:55,288 - INFO - power_cost_display - power_cost_display.py:104 - Successfully fetched data with 6 records
2025-07-08 16:04:55,288 - INFO - power_cost_display - power_cost_display.py:222 - Processing 'Without Banking' analysis
2025-07-08 16:04:55,288 - INFO - power_cost_display - power_cost_display.py:225 - Calculating monthly power costs without banking
2025-07-08 16:04:55,291 - INFO - power_cost_display - power_cost_display.py:233 - Successfully calculated power costs without banking, 6 records
2025-07-08 16:04:55,292 - INFO - power_cost_display - power_cost_display.py:238 - Successfully generated summary table for without banking analysis
2025-07-08 16:04:55,296 - INFO - power_cost_display - power_cost_display.py:279 - Successfully displayed without banking analysis metrics
2025-07-08 16:04:55,316 - INFO - power_cost_display - power_cost_display.py:290 - Successfully displayed interactive without banking cost chart
2025-07-08 16:04:55,320 - INFO - power_cost_display - power_cost_display.py:321 - Successfully displayed detailed without banking cost table
2025-07-08 16:05:25,196 - INFO - power_cost_display - power_cost_display.py:27 - Starting power cost analysis for plant: Kids Clinic India Limited
2025-07-08 16:05:25,197 - INFO - power_cost_display - power_cost_display.py:32 - Interactive plot setting: True
2025-07-08 16:05:25,199 - INFO - power_cost_display - power_cost_display.py:57 - Banking option selected: Without Banking
2025-07-08 16:05:25,200 - INFO - power_cost_display - power_cost_display.py:81 - Grid rate set to: 4.0
2025-07-08 16:05:25,200 - INFO - power_cost_display - power_cost_display.py:96 - Fetching combined monthly data for plant: Kids Clinic India Limited
2025-07-08 16:05:26,271 - INFO - power_cost_display - power_cost_display.py:104 - Successfully fetched data with 6 records
2025-07-08 16:05:26,271 - INFO - power_cost_display - power_cost_display.py:222 - Processing 'Without Banking' analysis
2025-07-08 16:05:26,271 - INFO - power_cost_display - power_cost_display.py:225 - Calculating monthly power costs without banking
2025-07-08 16:05:26,274 - INFO - power_cost_display - power_cost_display.py:233 - Successfully calculated power costs without banking, 6 records
2025-07-08 16:05:26,275 - INFO - power_cost_display - power_cost_display.py:238 - Successfully generated summary table for without banking analysis
2025-07-08 16:05:26,276 - INFO - power_cost_display - power_cost_display.py:279 - Successfully displayed without banking analysis metrics
2025-07-08 16:05:26,299 - INFO - power_cost_display - power_cost_display.py:290 - Successfully displayed interactive without banking cost chart
2025-07-08 16:05:26,302 - INFO - power_cost_display - power_cost_display.py:321 - Successfully displayed detailed without banking cost table
2025-07-08 16:05:51,133 - INFO - power_cost_display - power_cost_display.py:27 - Starting power cost analysis for plant: Kids Clinic India Limited
2025-07-08 16:05:51,134 - INFO - power_cost_display - power_cost_display.py:32 - Interactive plot setting: True
2025-07-08 16:05:51,134 - INFO - power_cost_display - power_cost_display.py:57 - Banking option selected: Without Banking
2025-07-08 16:05:51,135 - INFO - power_cost_display - power_cost_display.py:81 - Grid rate set to: 4.0
2025-07-08 16:05:51,135 - INFO - power_cost_display - power_cost_display.py:96 - Fetching combined monthly data for plant: Kids Clinic India Limited
2025-07-08 16:05:52,170 - INFO - power_cost_display - power_cost_display.py:104 - Successfully fetched data with 6 records
2025-07-08 16:05:52,170 - INFO - power_cost_display - power_cost_display.py:222 - Processing 'Without Banking' analysis
2025-07-08 16:05:52,171 - INFO - power_cost_display - power_cost_display.py:225 - Calculating monthly power costs without banking
2025-07-08 16:05:52,174 - INFO - power_cost_display - power_cost_display.py:233 - Successfully calculated power costs without banking, 6 records
2025-07-08 16:05:52,175 - INFO - power_cost_display - power_cost_display.py:238 - Successfully generated summary table for without banking analysis
2025-07-08 16:05:52,178 - INFO - power_cost_display - power_cost_display.py:279 - Successfully displayed without banking analysis metrics
2025-07-08 16:05:52,201 - INFO - power_cost_display - power_cost_display.py:290 - Successfully displayed interactive without banking cost chart
2025-07-08 16:05:52,204 - INFO - power_cost_display - power_cost_display.py:321 - Successfully displayed detailed without banking cost table
2025-07-08 16:07:19,639 - INFO - power_cost_display - power_cost_display.py:27 - Starting power cost analysis for plant: Kids Clinic India Limited
2025-07-08 16:07:19,639 - INFO - power_cost_display - power_cost_display.py:32 - Interactive plot setting: True
2025-07-08 16:07:19,639 - INFO - power_cost_display - power_cost_display.py:57 - Banking option selected: Without Banking
2025-07-08 16:07:19,639 - INFO - power_cost_display - power_cost_display.py:81 - Grid rate set to: 4.0
2025-07-08 16:07:19,639 - INFO - power_cost_display - power_cost_display.py:96 - Fetching combined monthly data for plant: Kids Clinic India Limited
2025-07-08 16:07:20,565 - INFO - power_cost_display - power_cost_display.py:104 - Successfully fetched data with 6 records
2025-07-08 16:07:20,565 - INFO - power_cost_display - power_cost_display.py:222 - Processing 'Without Banking' analysis
2025-07-08 16:07:20,565 - INFO - power_cost_display - power_cost_display.py:225 - Calculating monthly power costs without banking
2025-07-08 16:07:20,569 - INFO - power_cost_display - power_cost_display.py:233 - Successfully calculated power costs without banking, 6 records
2025-07-08 16:07:20,571 - INFO - power_cost_display - power_cost_display.py:238 - Successfully generated summary table for without banking analysis
2025-07-08 16:07:20,573 - INFO - power_cost_display - power_cost_display.py:279 - Successfully displayed without banking analysis metrics
2025-07-08 16:07:20,596 - INFO - power_cost_display - power_cost_display.py:290 - Successfully displayed interactive without banking cost chart
2025-07-08 16:07:20,601 - INFO - power_cost_display - power_cost_display.py:321 - Successfully displayed detailed without banking cost table
2025-07-08 16:07:51,669 - INFO - power_cost_display - power_cost_display.py:27 - Starting power cost analysis for plant: Kids Clinic India Limited
2025-07-08 16:07:51,669 - INFO - power_cost_display - power_cost_display.py:32 - Interactive plot setting: True
2025-07-08 16:07:51,669 - INFO - power_cost_display - power_cost_display.py:57 - Banking option selected: Without Banking
2025-07-08 16:07:51,669 - INFO - power_cost_display - power_cost_display.py:81 - Grid rate set to: 4.0
2025-07-08 16:07:51,675 - INFO - power_cost_display - power_cost_display.py:96 - Fetching combined monthly data for plant: Kids Clinic India Limited
2025-07-08 16:07:52,608 - INFO - power_cost_display - power_cost_display.py:104 - Successfully fetched data with 6 records
2025-07-08 16:07:52,609 - INFO - power_cost_display - power_cost_display.py:222 - Processing 'Without Banking' analysis
2025-07-08 16:07:52,609 - INFO - power_cost_display - power_cost_display.py:225 - Calculating monthly power costs without banking
2025-07-08 16:07:52,613 - INFO - power_cost_display - power_cost_display.py:233 - Successfully calculated power costs without banking, 6 records
2025-07-08 16:07:52,614 - INFO - power_cost_display - power_cost_display.py:238 - Successfully generated summary table for without banking analysis
2025-07-08 16:07:55,377 - INFO - power_cost_display - power_cost_display.py:27 - Starting power cost analysis for plant: Kids Clinic India Limited
2025-07-08 16:07:55,377 - INFO - power_cost_display - power_cost_display.py:32 - Interactive plot setting: True
2025-07-08 16:07:55,379 - INFO - power_cost_display - power_cost_display.py:57 - Banking option selected: Without Banking
2025-07-08 16:07:55,379 - INFO - power_cost_display - power_cost_display.py:81 - Grid rate set to: 4.0
2025-07-08 16:07:55,379 - INFO - power_cost_display - power_cost_display.py:96 - Fetching combined monthly data for plant: Kids Clinic India Limited
2025-07-08 16:07:56,414 - INFO - power_cost_display - power_cost_display.py:104 - Successfully fetched data with 6 records
2025-07-08 16:07:56,414 - INFO - power_cost_display - power_cost_display.py:222 - Processing 'Without Banking' analysis
2025-07-08 16:07:56,414 - INFO - power_cost_display - power_cost_display.py:225 - Calculating monthly power costs without banking
2025-07-08 16:07:56,418 - INFO - power_cost_display - power_cost_display.py:233 - Successfully calculated power costs without banking, 6 records
2025-07-08 16:07:56,419 - INFO - power_cost_display - power_cost_display.py:238 - Successfully generated summary table for without banking analysis
2025-07-08 16:07:56,421 - INFO - power_cost_display - power_cost_display.py:279 - Successfully displayed without banking analysis metrics
2025-07-08 16:07:56,439 - INFO - power_cost_display - power_cost_display.py:290 - Successfully displayed interactive without banking cost chart
2025-07-08 16:07:56,443 - INFO - power_cost_display - power_cost_display.py:321 - Successfully displayed detailed without banking cost table
2025-07-09 12:04:42,315 - INFO - power_cost_display - power_cost_display.py:27 - Starting power cost analysis for plant: Kids Clinic India Limited
2025-07-09 12:04:42,379 - INFO - power_cost_display - power_cost_display.py:32 - Interactive plot setting: True
2025-07-09 12:04:42,387 - INFO - power_cost_display - power_cost_display.py:57 - Banking option selected: Without Banking
2025-07-09 12:04:42,387 - INFO - power_cost_display - power_cost_display.py:81 - Grid rate set to: 4.0
2025-07-09 12:04:42,387 - INFO - power_cost_display - power_cost_display.py:96 - Fetching combined monthly data for plant: Kids Clinic India Limited
2025-07-09 12:04:45,245 - INFO - power_cost_display - power_cost_display.py:104 - Successfully fetched data with 6 records
2025-07-09 12:04:45,245 - INFO - power_cost_display - power_cost_display.py:222 - Processing 'Without Banking' analysis
2025-07-09 12:04:45,246 - INFO - power_cost_display - power_cost_display.py:225 - Calculating monthly power costs without banking
2025-07-09 12:04:45,249 - INFO - power_cost_display - power_cost_display.py:233 - Successfully calculated power costs without banking, 6 records
2025-07-09 12:04:45,249 - INFO - power_cost_display - power_cost_display.py:238 - Successfully generated summary table for without banking analysis
2025-07-09 12:04:45,261 - INFO - power_cost_display - power_cost_display.py:279 - Successfully displayed without banking analysis metrics
2025-07-09 12:04:45,373 - INFO - power_cost_display - power_cost_display.py:290 - Successfully displayed interactive without banking cost chart
2025-07-09 12:04:45,381 - INFO - power_cost_display - power_cost_display.py:321 - Successfully displayed detailed without banking cost table
2025-07-09 12:06:50,727 - INFO - power_cost_display - power_cost_display.py:27 - Starting power cost analysis for plant: Kids Clinic India Limited
2025-07-09 12:06:50,727 - INFO - power_cost_display - power_cost_display.py:32 - Interactive plot setting: True
2025-07-09 12:06:50,732 - INFO - power_cost_display - power_cost_display.py:57 - Banking option selected: Without Banking
2025-07-09 12:06:50,733 - INFO - power_cost_display - power_cost_display.py:81 - Grid rate set to: 4.0
2025-07-09 12:06:50,733 - INFO - power_cost_display - power_cost_display.py:96 - Fetching combined monthly data for plant: Kids Clinic India Limited
2025-07-09 12:06:51,956 - INFO - power_cost_display - power_cost_display.py:104 - Successfully fetched data with 6 records
2025-07-09 12:06:51,956 - INFO - power_cost_display - power_cost_display.py:222 - Processing 'Without Banking' analysis
2025-07-09 12:06:51,956 - INFO - power_cost_display - power_cost_display.py:225 - Calculating monthly power costs without banking
2025-07-09 12:06:51,956 - INFO - power_cost_display - power_cost_display.py:233 - Successfully calculated power costs without banking, 6 records
2025-07-09 12:06:51,956 - INFO - power_cost_display - power_cost_display.py:238 - Successfully generated summary table for without banking analysis
2025-07-09 12:06:51,969 - INFO - power_cost_display - power_cost_display.py:279 - Successfully displayed without banking analysis metrics
2025-07-09 12:06:51,996 - INFO - power_cost_display - power_cost_display.py:290 - Successfully displayed interactive without banking cost chart
2025-07-09 12:06:52,000 - INFO - power_cost_display - power_cost_display.py:321 - Successfully displayed detailed without banking cost table
2025-07-09 12:07:24,959 - INFO - power_cost_display - power_cost_display.py:27 - Starting power cost analysis for plant: Kids Clinic India Limited
2025-07-09 12:07:24,959 - INFO - power_cost_display - power_cost_display.py:32 - Interactive plot setting: True
2025-07-09 12:07:24,959 - INFO - power_cost_display - power_cost_display.py:57 - Banking option selected: Without Banking
2025-07-09 12:07:24,959 - INFO - power_cost_display - power_cost_display.py:81 - Grid rate set to: 4.0
2025-07-09 12:07:24,959 - INFO - power_cost_display - power_cost_display.py:96 - Fetching combined monthly data for plant: Kids Clinic India Limited
2025-07-09 12:07:27,718 - INFO - power_cost_display - power_cost_display.py:104 - Successfully fetched data with 6 records
2025-07-09 12:07:27,718 - INFO - power_cost_display - power_cost_display.py:222 - Processing 'Without Banking' analysis
2025-07-09 12:07:27,733 - INFO - power_cost_display - power_cost_display.py:225 - Calculating monthly power costs without banking
2025-07-09 12:07:27,733 - INFO - power_cost_display - power_cost_display.py:233 - Successfully calculated power costs without banking, 6 records
2025-07-09 12:07:27,733 - INFO - power_cost_display - power_cost_display.py:238 - Successfully generated summary table for without banking analysis
2025-07-09 12:07:27,751 - INFO - power_cost_display - power_cost_display.py:279 - Successfully displayed without banking analysis metrics
2025-07-09 14:03:02,433 - INFO - power_cost_display - power_cost_display.py:27 - Starting power cost analysis for plant: Kids Clinic India Limited
2025-07-09 14:03:02,434 - INFO - power_cost_display - power_cost_display.py:32 - Interactive plot setting: True
2025-07-09 14:03:02,437 - INFO - power_cost_display - power_cost_display.py:57 - Banking option selected: Without Banking
2025-07-09 14:03:02,439 - INFO - power_cost_display - power_cost_display.py:81 - Grid rate set to: 4.0
2025-07-09 14:03:02,439 - INFO - power_cost_display - power_cost_display.py:96 - Fetching combined monthly data for plant: Kids Clinic India Limited
2025-07-09 14:03:03,968 - INFO - power_cost_display - power_cost_display.py:104 - Successfully fetched data with 6 records
2025-07-09 14:03:03,968 - INFO - power_cost_display - power_cost_display.py:222 - Processing 'Without Banking' analysis
2025-07-09 14:03:03,968 - INFO - power_cost_display - power_cost_display.py:225 - Calculating monthly power costs without banking
2025-07-09 14:03:03,968 - INFO - power_cost_display - power_cost_display.py:233 - Successfully calculated power costs without banking, 6 records
2025-07-09 14:03:03,983 - INFO - power_cost_display - power_cost_display.py:238 - Successfully generated summary table for without banking analysis
2025-07-09 14:03:03,985 - INFO - power_cost_display - power_cost_display.py:279 - Successfully displayed without banking analysis metrics
2025-07-09 14:04:25,184 - INFO - power_cost_display - power_cost_display.py:27 - Starting power cost analysis for plant: Kids Clinic India Limited
2025-07-09 14:04:25,184 - INFO - power_cost_display - power_cost_display.py:32 - Interactive plot setting: True
2025-07-09 14:04:25,194 - INFO - power_cost_display - power_cost_display.py:57 - Banking option selected: Without Banking
2025-07-09 14:04:25,197 - INFO - power_cost_display - power_cost_display.py:85 - Grid rate set to: 4.0
2025-07-09 14:04:25,202 - INFO - power_cost_display - power_cost_display.py:104 - Renewable rate set to: 2.0
2025-07-09 14:04:25,202 - INFO - power_cost_display - power_cost_display.py:126 - Fetching combined monthly data for plant: Kids Clinic India Limited
2025-07-09 14:04:27,215 - INFO - power_cost_display - power_cost_display.py:134 - Successfully fetched data with 6 records
2025-07-09 14:04:27,215 - INFO - power_cost_display - power_cost_display.py:252 - Processing 'Without Banking' analysis
2025-07-09 14:04:27,215 - INFO - power_cost_display - power_cost_display.py:255 - Calculating monthly power costs without banking
2025-07-09 14:04:27,215 - INFO - power_cost_display - power_cost_display.py:263 - Successfully calculated power costs without banking, 6 records
2025-07-09 14:04:27,215 - INFO - power_cost_display - power_cost_display.py:268 - Successfully generated summary table for without banking analysis
2025-07-09 14:04:27,215 - INFO - power_cost_display - power_cost_display.py:309 - Successfully displayed without banking analysis metrics
2025-07-09 14:04:42,667 - INFO - power_cost_display - power_cost_display.py:27 - Starting power cost analysis for plant: Kids Clinic India Limited
2025-07-09 14:04:42,667 - INFO - power_cost_display - power_cost_display.py:32 - Interactive plot setting: True
2025-07-09 14:04:42,669 - INFO - power_cost_display - power_cost_display.py:57 - Banking option selected: Without Banking
2025-07-09 14:04:42,671 - INFO - power_cost_display - power_cost_display.py:85 - Grid rate set to: 4.0
2025-07-09 14:04:42,672 - INFO - power_cost_display - power_cost_display.py:104 - Renewable rate set to: 2.0
2025-07-09 14:04:42,672 - INFO - power_cost_display - power_cost_display.py:126 - Fetching combined monthly data for plant: Kids Clinic India Limited
2025-07-09 14:04:43,581 - INFO - power_cost_display - power_cost_display.py:134 - Successfully fetched data with 6 records
2025-07-09 14:04:43,582 - INFO - power_cost_display - power_cost_display.py:252 - Processing 'Without Banking' analysis
2025-07-09 14:04:43,582 - INFO - power_cost_display - power_cost_display.py:255 - Calculating monthly power costs without banking
2025-07-09 14:04:43,585 - INFO - power_cost_display - power_cost_display.py:263 - Successfully calculated power costs without banking, 6 records
2025-07-09 14:04:43,585 - INFO - power_cost_display - power_cost_display.py:268 - Successfully generated summary table for without banking analysis
2025-07-09 14:04:43,588 - INFO - power_cost_display - power_cost_display.py:309 - Successfully displayed without banking analysis metrics
2025-07-09 14:05:01,942 - INFO - power_cost_display - power_cost_display.py:27 - Starting power cost analysis for plant: Kids Clinic India Limited
2025-07-09 14:05:01,942 - INFO - power_cost_display - power_cost_display.py:32 - Interactive plot setting: True
2025-07-09 14:05:01,944 - INFO - power_cost_display - power_cost_display.py:57 - Banking option selected: Without Banking
2025-07-09 14:05:01,944 - INFO - power_cost_display - power_cost_display.py:85 - Grid rate set to: 4.0
2025-07-09 14:05:01,944 - INFO - power_cost_display - power_cost_display.py:104 - Renewable rate set to: 6.0
2025-07-09 14:05:01,944 - INFO - power_cost_display - power_cost_display.py:126 - Fetching combined monthly data for plant: Kids Clinic India Limited
2025-07-09 14:05:03,118 - INFO - power_cost_display - power_cost_display.py:134 - Successfully fetched data with 6 records
2025-07-09 14:05:03,118 - INFO - power_cost_display - power_cost_display.py:252 - Processing 'Without Banking' analysis
2025-07-09 14:05:03,118 - INFO - power_cost_display - power_cost_display.py:255 - Calculating monthly power costs without banking
2025-07-09 14:05:03,129 - INFO - power_cost_display - power_cost_display.py:263 - Successfully calculated power costs without banking, 6 records
2025-07-09 14:05:03,131 - INFO - power_cost_display - power_cost_display.py:268 - Successfully generated summary table for without banking analysis
2025-07-09 14:05:03,135 - INFO - power_cost_display - power_cost_display.py:309 - Successfully displayed without banking analysis metrics
2025-07-09 14:05:15,850 - INFO - power_cost_display - power_cost_display.py:27 - Starting power cost analysis for plant: Kids Clinic India Limited
2025-07-09 14:05:15,851 - INFO - power_cost_display - power_cost_display.py:32 - Interactive plot setting: True
2025-07-09 14:05:15,853 - INFO - power_cost_display - power_cost_display.py:57 - Banking option selected: Without Banking
2025-07-09 14:05:15,854 - INFO - power_cost_display - power_cost_display.py:85 - Grid rate set to: 4.0
2025-07-09 14:05:15,855 - INFO - power_cost_display - power_cost_display.py:104 - Renewable rate set to: 1.0
2025-07-09 14:05:15,855 - INFO - power_cost_display - power_cost_display.py:126 - Fetching combined monthly data for plant: Kids Clinic India Limited
2025-07-09 14:05:16,788 - INFO - power_cost_display - power_cost_display.py:134 - Successfully fetched data with 6 records
2025-07-09 14:05:16,788 - INFO - power_cost_display - power_cost_display.py:252 - Processing 'Without Banking' analysis
2025-07-09 14:05:16,788 - INFO - power_cost_display - power_cost_display.py:255 - Calculating monthly power costs without banking
2025-07-09 14:05:16,801 - INFO - power_cost_display - power_cost_display.py:263 - Successfully calculated power costs without banking, 6 records
2025-07-09 14:05:16,802 - INFO - power_cost_display - power_cost_display.py:268 - Successfully generated summary table for without banking analysis
2025-07-09 14:05:16,805 - INFO - power_cost_display - power_cost_display.py:309 - Successfully displayed without banking analysis metrics
2025-07-09 14:27:36,145 - INFO - power_cost_display - power_cost_display.py:27 - Starting power cost analysis for plant: Kids Clinic India Limited
2025-07-09 14:27:36,145 - INFO - power_cost_display - power_cost_display.py:32 - Interactive plot setting: True
2025-07-09 14:27:36,147 - INFO - power_cost_display - power_cost_display.py:57 - Banking option selected: Without Banking
2025-07-09 14:27:36,151 - INFO - power_cost_display - power_cost_display.py:85 - Grid rate set to: 4.0
2025-07-09 14:27:36,152 - INFO - power_cost_display - power_cost_display.py:104 - Renewable rate set to: 1.0
2025-07-09 14:27:36,153 - INFO - power_cost_display - power_cost_display.py:126 - Fetching combined monthly data for plant: Kids Clinic India Limited
2025-07-09 14:27:37,693 - INFO - power_cost_display - power_cost_display.py:134 - Successfully fetched data with 6 records
2025-07-09 14:27:37,695 - INFO - power_cost_display - power_cost_display.py:252 - Processing 'Without Banking' analysis
2025-07-09 14:27:37,695 - INFO - power_cost_display - power_cost_display.py:255 - Calculating monthly power costs without banking
2025-07-09 14:27:37,701 - INFO - power_cost_display - power_cost_display.py:263 - Successfully calculated power costs without banking, 6 records
2025-07-09 14:27:37,701 - INFO - power_cost_display - power_cost_display.py:268 - Successfully generated summary table for without banking analysis
2025-07-09 14:27:37,707 - INFO - power_cost_display - power_cost_display.py:309 - Successfully displayed without banking analysis metrics
2025-07-09 14:28:35,520 - INFO - power_cost_display - power_cost_display.py:27 - Starting power cost analysis for plant: Kids Clinic India Limited
2025-07-09 14:28:35,520 - INFO - power_cost_display - power_cost_display.py:32 - Interactive plot setting: True
2025-07-09 14:28:35,520 - INFO - power_cost_display - power_cost_display.py:57 - Banking option selected: Without Banking
2025-07-09 14:28:35,520 - INFO - power_cost_display - power_cost_display.py:85 - Grid rate set to: 4.0
2025-07-09 14:28:35,520 - INFO - power_cost_display - power_cost_display.py:104 - Renewable rate set to: 2.0
2025-07-09 14:28:35,520 - INFO - power_cost_display - power_cost_display.py:126 - Fetching combined monthly data for plant: Kids Clinic India Limited
2025-07-09 14:28:37,272 - INFO - power_cost_display - power_cost_display.py:134 - Successfully fetched data with 6 records
2025-07-09 14:28:37,273 - INFO - power_cost_display - power_cost_display.py:252 - Processing 'Without Banking' analysis
2025-07-09 14:28:37,273 - INFO - power_cost_display - power_cost_display.py:255 - Calculating monthly power costs without banking
2025-07-09 14:28:37,275 - INFO - power_cost_display - power_cost_display.py:263 - Successfully calculated power costs without banking, 6 records
2025-07-09 14:28:37,275 - INFO - power_cost_display - power_cost_display.py:268 - Successfully generated summary table for without banking analysis
2025-07-09 14:28:45,007 - INFO - power_cost_display - power_cost_display.py:27 - Starting power cost analysis for plant: Kids Clinic India Limited
2025-07-09 14:28:45,007 - INFO - power_cost_display - power_cost_display.py:32 - Interactive plot setting: True
2025-07-09 14:28:45,013 - INFO - power_cost_display - power_cost_display.py:57 - Banking option selected: With Banking
2025-07-09 14:28:45,013 - INFO - power_cost_display - power_cost_display.py:85 - Grid rate set to: 4.0
2025-07-09 14:28:45,017 - INFO - power_cost_display - power_cost_display.py:104 - Renewable rate set to: 2.0
2025-07-09 14:28:45,019 - INFO - power_cost_display - power_cost_display.py:126 - Fetching combined monthly data for plant: Kids Clinic India Limited
2025-07-09 14:28:46,799 - INFO - power_cost_display - power_cost_display.py:134 - Successfully fetched data with 6 records
2025-07-09 14:28:46,799 - INFO - power_cost_display - power_cost_display.py:143 - Processing 'With Banking' analysis
2025-07-09 14:28:46,799 - INFO - power_cost_display - power_cost_display.py:146 - Calculating monthly power costs with banking
2025-07-09 14:28:46,806 - INFO - power_cost_display - power_cost_display.py:154 - Successfully calculated power costs with banking, 6 records
2025-07-09 14:28:46,806 - INFO - power_cost_display - power_cost_display.py:159 - Successfully generated summary table for banking analysis
2025-07-09 14:28:46,812 - INFO - power_cost_display - power_cost_display.py:200 - Successfully displayed banking analysis metrics
2025-07-09 14:49:46,441 - INFO - power_cost_display - power_cost_display.py:27 - Starting power cost analysis for plant: Kids Clinic India Limited
2025-07-09 14:49:46,442 - INFO - power_cost_display - power_cost_display.py:32 - Interactive plot setting: True
2025-07-09 14:49:46,443 - INFO - power_cost_display - power_cost_display.py:57 - Banking option selected: Without Banking
2025-07-09 14:49:46,447 - INFO - power_cost_display - power_cost_display.py:85 - Grid rate set to: 4.0
2025-07-09 14:49:46,448 - INFO - power_cost_display - power_cost_display.py:104 - Renewable rate set to: 2.0
2025-07-09 14:49:46,452 - INFO - power_cost_display - power_cost_display.py:126 - Fetching combined monthly data for plant: Kids Clinic India Limited
2025-07-09 14:49:48,140 - INFO - power_cost_display - power_cost_display.py:134 - Successfully fetched data with 6 records
2025-07-09 14:49:48,142 - INFO - power_cost_display - power_cost_display.py:252 - Processing 'Without Banking' analysis
2025-07-09 14:49:48,142 - INFO - power_cost_display - power_cost_display.py:255 - Calculating monthly power costs without banking
2025-07-09 14:49:48,146 - INFO - power_cost_display - power_cost_display.py:263 - Successfully calculated power costs without banking, 6 records
2025-07-09 14:49:48,148 - INFO - power_cost_display - power_cost_display.py:268 - Successfully generated summary table for without banking analysis
2025-07-09 14:49:48,152 - INFO - power_cost_display - power_cost_display.py:309 - Successfully displayed without banking analysis metrics
2025-07-09 14:58:21,655 - INFO - power_cost_display - power_cost_display.py:27 - Starting power cost analysis for plant: Kids Clinic India Limited
2025-07-09 14:58:21,655 - INFO - power_cost_display - power_cost_display.py:32 - Interactive plot setting: True
2025-07-09 14:58:21,667 - INFO - power_cost_display - power_cost_display.py:57 - Banking option selected: Without Banking
2025-07-09 14:58:21,669 - INFO - power_cost_display - power_cost_display.py:85 - Grid rate set to: 4.0
2025-07-09 14:58:21,672 - INFO - power_cost_display - power_cost_display.py:104 - Renewable rate set to: 2.0
2025-07-09 14:58:21,673 - INFO - power_cost_display - power_cost_display.py:126 - Fetching combined monthly data for plant: Kids Clinic India Limited
2025-07-09 14:58:23,338 - INFO - power_cost_display - power_cost_display.py:134 - Successfully fetched data with 6 records
2025-07-09 14:58:23,338 - INFO - power_cost_display - power_cost_display.py:252 - Processing 'Without Banking' analysis
2025-07-09 14:58:23,339 - INFO - power_cost_display - power_cost_display.py:255 - Calculating monthly power costs without banking
2025-07-09 14:58:23,343 - INFO - power_cost_display - power_cost_display.py:263 - Successfully calculated power costs without banking, 6 records
2025-07-09 14:58:23,345 - INFO - power_cost_display - power_cost_display.py:268 - Successfully generated summary table for without banking analysis
2025-07-09 14:58:23,349 - INFO - power_cost_display - power_cost_display.py:309 - Successfully displayed without banking analysis metrics
