import streamlit as st
import pandas as pd
from db.fetch_tod_tab_data import fetch_combined_monthly_data
from db.db_setup import CONN
from visualizations.power_cost_calculations import (
    calculate_monthly_power_costs, 
    summarize_costs_table, 
    calculate_monthly_costs_without_banking, 
    summarize_costs_table_without_banking
)
from visualizations.power_cost_visual import (
    plot_costs_with_banking, 
    plot_costs_without_banking,
    plot_costs_with_banking_interactive,
    plot_costs_without_banking_interactive
)
from frontend.ui_components.dashboard_controls import get_interactive_plot_setting


from helper.setup_logger import setup_logger

logging = setup_logger("power_cost_display", "power_cost_display.log")

def display_power_cost_analysis(selected_plant):
    """Display power cost analysis with banking and without banking options."""
    
    logging.info(f"Starting power cost analysis for plant: {selected_plant}")
    
    # Get universal plot options setting
    try:
        use_interactive = get_interactive_plot_setting()
        logging.info(f"Interactive plot setting: {use_interactive}")
    except Exception as e:
        logging.warning(f"Error getting interactive plot setting, using default: {str(e)}")
        use_interactive = False
    
    # Power cost input section with right-aligned input
    col_left, col_right = st.columns([3, 1])

    with col_left:
        try:
            # Initialize session state for banking option if not exists
            banking_key = f"power_cost_banking_option_{selected_plant}"
            if banking_key not in st.session_state:
                st.session_state[banking_key] = "Without Banking"
            
            # Add radio button for banking option selection with unique key to prevent global reruns
            banking_option = st.radio(
                "Select Analysis Type:",
                options=["Without Banking", "With Banking"],
                index=0 if st.session_state[banking_key] == "Without Banking" else 1,
                horizontal=True,
                key=banking_key,  # Unique key per plant
                help="Choose whether to include banking in the power cost analysis",
                on_change=lambda: None  # Prevent callback propagation
            )
            logging.info(f"Banking option selected: {banking_option}")
        except Exception as e:
            logging.error(f"Error creating banking option selector: {str(e)}")
            st.error("There was an issue with the banking option selector. Please refresh the page.")
            return

    with col_right:
        try:
            # Create two columns for grid and renewable cost inputs
            cost_col1, cost_col2 = st.columns(2)

            with cost_col1:
                # Initialize session state for grid rate if not exists
                grid_rate_key = f"power_cost_grid_rate_{selected_plant}"
                if grid_rate_key not in st.session_state:
                    st.session_state[grid_rate_key] = 4.0

                # Compact grid power cost input
                grid_rate = st.number_input(
                    "Grid Cost (₹/kWh)",
                    min_value=0.01,  # Changed from 0.0 to prevent division issues
                    max_value=50.0,
                    value=st.session_state[grid_rate_key],
                    step=0.1,
                    key=grid_rate_key,  # Unique key per plant
                    help="Enter grid electricity cost per kWh",
                    on_change=lambda: None  # Prevent callback propagation
                )
                logging.info(f"Grid rate set to: {grid_rate}")

            with cost_col2:
                # Initialize session state for renewable rate if not exists
                renewable_rate_key = f"power_cost_renewable_rate_{selected_plant}"
                if renewable_rate_key not in st.session_state:
                    st.session_state[renewable_rate_key] = 2.0

                # Compact renewable power cost input
                renewable_rate = st.number_input(
                    "Renewable Cost (₹/kWh)",
                    min_value=0.01,  # Changed from 0.0 to prevent division issues
                    max_value=50.0,
                    value=st.session_state[renewable_rate_key],
                    step=0.1,
                    key=renewable_rate_key,  # Unique key per plant
                    help="Enter renewable electricity cost per kWh",
                    on_change=lambda: None  # Prevent callback propagation
                )
                logging.info(f"Renewable rate set to: {renewable_rate}")

        except Exception as e:
            logging.error(f"Error creating cost inputs: {str(e)}")
            st.error("There was an issue with the cost inputs. Please refresh the page.")
            return

    # Add error handling and validation
    try:
        # Validate grid_rate
        if grid_rate is None or grid_rate <= 0:
            logging.warning(f"Invalid grid rate provided: {grid_rate}")
            st.error("Please enter a valid grid cost value greater than 0")
            return

        # Validate renewable_rate
        if renewable_rate is None or renewable_rate <= 0:
            logging.warning(f"Invalid renewable rate provided: {renewable_rate}")
            st.error("Please enter a valid renewable cost value greater than 0")
            return
            
        # Fetch data with error handling
        logging.info(f"Fetching combined monthly data for plant: {selected_plant}")
        main_df = fetch_combined_monthly_data(CONN, selected_plant)
        
        if main_df is None or main_df.empty:
            logging.warning(f"No data available for plant: {selected_plant}")
            st.warning("⚠️ No data available for the selected plant. Please try selecting a different plant or check if data exists for this plant.")
            return
            
        logging.info(f"Successfully fetched data with {len(main_df)} records")
            
    except Exception as e:
        logging.error(f"Error in power cost analysis data fetching: {str(e)}")
        st.error("We're experiencing technical difficulties loading the power cost data. Please try again later or contact support if the issue persists.")
        return
    
    # Display content based on selected banking option
    if banking_option == "With Banking":
        logging.info("Processing 'With Banking' analysis")
        try:
            # Calculate costs with banking
            logging.info("Calculating monthly power costs with banking")
            df_calculated = calculate_monthly_power_costs(main_df, grid_rate, renewable_rate)
            
            if df_calculated is None or df_calculated.empty:
                logging.warning("Power cost calculation with banking returned empty result")
                st.warning("⚠️ Unable to calculate power costs with banking for the current data. Please check if sufficient data is available.")
                return

            logging.info(f"Successfully calculated power costs with banking, {len(df_calculated)} records")

            # Get summary data
            try:
                summary = summarize_costs_table(df_calculated)
                logging.info("Successfully generated summary table for banking analysis")
            except Exception as e:
                logging.error(f"Error generating summary table: {str(e)}")
                st.error("There was an issue generating the cost summary. Please try again or contact support.")
                return
            
            st.subheader("With Banking")
            
            # Display as metrics in table format
            try:
                col1, col2, col3, col4 = st.columns(4)
                
                with col1:
                    st.metric(
                        "Total Grid Cost",
                        f"₹{summary.iloc[0]['Total Grid Cost (₹)']/100000:.2f}L",
                        help="Total cost if all energy was purchased from grid"
                    )

                with col2:
                    st.metric(
                        "Actual Cost",
                        f"₹{summary.iloc[0]['Total Actual Cost (₹)']/100000:.2f}L",
                        help="Actual cost after solar/wind generation offset"
                    )

                with col3:
                    st.metric(
                        "Total Savings",
                        f"₹{summary.iloc[0]['Total Savings (₹)']/100000:.2f}L",
                        delta=f"{summary.iloc[0]['Savings (%)']:.1f}%",
                        help="Total money saved due to renewable generation"
                    )

                with col4:
                    st.metric(
                        "Energy Offset",
                        f"{summary.iloc[0]['Energy Offset']:.0f} kWh",
                        help="Total energy offset by renewable generation"
                    )
                
                logging.info("Successfully displayed banking analysis metrics")
            except Exception as e:
                logging.error(f"Error displaying banking analysis metrics: {str(e)}")
                st.error("There was an issue displaying the cost metrics. Please try again or contact support.")

            # Plot chart
            # try:
            #     if use_interactive:
            #         fig = plot_costs_with_banking_interactive(df_calculated, selected_plant)
            #         if fig:
            #             st.plotly_chart(fig, use_container_width=True)
            #             logging.info("Successfully displayed interactive banking cost chart")
            #         else:
            #             logging.warning("Interactive banking cost chart generation returned None")
            #             st.warning("⚠️ Unable to generate the interactive cost chart. Please try refreshing the page.")
            #     else:
            #         fig = plot_costs_with_banking(df_calculated, selected_plant)
            #         if fig:
            #             st.pyplot(fig)
            #             logging.info("Successfully displayed static banking cost chart")
            #         else:
            #             logging.warning("Static banking cost chart generation returned None")
            #             st.warning("⚠️ Unable to generate the cost chart. Please try refreshing the page.")
            # except Exception as e:
            #     logging.error(f"Error generating banking cost chart: {str(e)}")
            #     st.error("There was an issue creating the cost chart. Please try again or contact support.")
            
            # # Display the detailed table
            # try:
            #     st.subheader("Monthly Power Cost Analysis (With Banking)")
                
            #     # Convert monetary columns to lakhs for display
            #     df_display = df_calculated.copy()
            #     monetary_columns = [col for col in df_display.columns if 
            #                       any(keyword in col.lower() for keyword in ['cost', '₹', 'rupee']) and 
            #                       '%' not in col.lower() and 'percentage' not in col.lower()]
                
            #     for col in monetary_columns:
            #         if df_display[col].dtype in ['float64', 'int64']:
            #             df_display[col] = df_display[col].apply(lambda x: f"₹{x/100000:.2f}L" if pd.notna(x) and x != 0 else "₹0.00L")
                
            #     st.dataframe(df_display, use_container_width=True)
            #     logging.info("Successfully displayed detailed banking cost table")
            # except Exception as e:
            #     logging.error(f"Error displaying detailed banking cost table: {str(e)}")
            #     st.error("There was an issue displaying the detailed cost table. Please try again or contact support.")
            
        except Exception as e:
            logging.error(f"Error in with banking analysis: {str(e)}")
            st.error("We're experiencing technical difficulties with the banking analysis. Please try again later or contact support if the issue persists.")
    
    else:  # Without Banking
        logging.info("Processing 'Without Banking' analysis")
        try:
            # Calculate costs without banking
            logging.info("Calculating monthly power costs without banking")
            df_calculated_without_banking = calculate_monthly_costs_without_banking(main_df, grid_rate, renewable_rate)
            
            if df_calculated_without_banking is None or df_calculated_without_banking.empty:
                logging.warning("Power cost calculation without banking returned empty result")
                st.warning("⚠️ Unable to calculate power costs without banking for the current data. Please check if sufficient data is available.")
                return
            
            logging.info(f"Successfully calculated power costs without banking, {len(df_calculated_without_banking)} records")
            
            # Get summary data without banking
            try:
                summary_without_banking = summarize_costs_table_without_banking(df_calculated_without_banking)
                logging.info("Successfully generated summary table for without banking analysis")
            except Exception as e:
                logging.error(f"Error generating summary table without banking: {str(e)}")
                st.error("There was an issue generating the cost summary. Please try again or contact support.")
                return
            
            st.subheader("Without Banking")
            
            # Display as metrics in table format for Without Banking
            try:
                col1_nb, col2_nb, col3_nb, col4_nb = st.columns(4)
                
                with col1_nb:
                    st.metric(
                        "Total Grid Cost",
                        f"₹{summary_without_banking.iloc[0]['Total Grid Cost (₹)']/100000:.2f}L",
                        help="Total cost if all energy was purchased from grid"
                    )

                with col2_nb:
                    st.metric(
                        "Actual Cost",
                        f"₹{summary_without_banking.iloc[0]['Total Actual Cost (₹)']/100000:.2f}L",
                        help="Actual cost after solar/wind generation offset"
                    )

                with col3_nb:
                    st.metric(
                        "Total Savings",
                        f"₹{summary_without_banking.iloc[0]['Total Savings (₹)']/100000:.2f}L",
                        delta=f"{summary_without_banking.iloc[0]['Average Savings (%)']:.1f}%",
                        help="Total money saved due to renewable generation"
                    )

                with col4_nb:
                    st.metric(
                        "Energy Offset",
                        f"{summary_without_banking.iloc[0]['Energy Offset']:.0f} kWh",
                        help="Total energy offset by renewable generation"
                    )
                
                logging.info("Successfully displayed without banking analysis metrics")
            except Exception as e:
                logging.error(f"Error displaying without banking analysis metrics: {str(e)}")
                st.error("There was an issue displaying the cost metrics. Please try again or contact support.")

            # Plot chart
            # try:
            #     if use_interactive:
            #         fig = plot_costs_without_banking_interactive(df_calculated_without_banking, selected_plant)
            #         if fig:
            #             st.plotly_chart(fig, use_container_width=True)
            #             logging.info("Successfully displayed interactive without banking cost chart")
            #         else:
            #             logging.warning("Interactive without banking cost chart generation returned None")
            #             st.warning("⚠️ Unable to generate the interactive cost chart. Please try refreshing the page.")
            #     else:
            #         fig = plot_costs_without_banking(df_calculated_without_banking, selected_plant)
            #         if fig:
            #             st.pyplot(fig)
            #             logging.info("Successfully displayed static without banking cost chart")
            #         else:
            #             logging.warning("Static without banking cost chart generation returned None")
            #             st.warning("⚠️ Unable to generate the cost chart. Please try refreshing the page.")
            # except Exception as e:
            #     logging.error(f"Error generating without banking cost chart: {str(e)}")
            #     st.error("There was an issue creating the cost chart. Please try again or contact support.")
            
            # # Display the detailed table without banking
            # try:
            #     st.subheader("Monthly Power Cost Analysis (Without Banking)")
                
            #     # Convert monetary columns to lakhs for display
            #     df_display_without_banking = df_calculated_without_banking.copy()
            #     monetary_columns_nb = [col for col in df_display_without_banking.columns if 
            #                          any(keyword in col.lower() for keyword in ['cost', '₹', 'rupee']) and 
            #                          '%' not in col.lower() and 'percentage' not in col.lower()]
                
            #     for col in monetary_columns_nb:
            #         if df_display_without_banking[col].dtype in ['float64', 'int64']:
            #             df_display_without_banking[col] = df_display_without_banking[col].apply(lambda x: f"₹{x/100000:.2f}L" if pd.notna(x) and x != 0 else "₹0.00L")
                
            #     st.dataframe(df_display_without_banking, use_container_width=True)
            #     logging.info("Successfully displayed detailed without banking cost table")
            # except Exception as e:
            #     logging.error(f"Error displaying detailed without banking cost table: {str(e)}")
            #     st.error("There was an issue displaying the detailed cost table. Please try again or contact support.")
            
        except Exception as e:
            logging.error(f"Error in without banking analysis: {str(e)}")
            st.error("We're experiencing technical difficulties with the without banking analysis. Please try again later or contact support if the issue persists.")


